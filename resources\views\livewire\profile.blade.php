<?php
use Illuminate\Support\Facades\Auth;
?>

<div wire:poll.300s="refreshStatus"> {{-- Atualiza status a cada 5 minutos (300s) --}}
    <livewire:user-images />
    <livewire:user-videos />
    <livewire:user-following />
    <livewire:user-followers />
    <livewire:user-posts />

    {{-- Profile header  --}}
    <div
        x-data="{ show: false }"
        x-init="setTimeout(() => show = true, 200)"
        x-transition:enter="transition ease-out duration-700"
        x-transition:enter-start="opacity-0 scale-90"
        x-transition:enter-end="opacity-100 scale-100"
        x-show="show"
        class="relative w-full bg-white dark:bg-zinc-800 shadow-lg rounded-lg overflow-hidden">

        <div id="profile_header" class="relative w-full group">
            {{-- Foto de capa Backgrond cover --}}
            <div class="w-full h-80 bg-cover bg-center" style="background-image: url('{{ $this->cover() ?? asset('images/users/capa.svg') }}');"></div>

            @if($this->isOwner())
                <!-- Botão para editar capa -->
                <div class="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <flux:button
                        href="{{ route('settings.profile-with-cover') }}"
                        wire:navigate
                        variant="primary"
                        size="sm"
                        class="text-white bg-zinc-800 hover:bg-zinc-700 border-none shadow-lg"
                    >
                        <flux:icon.photo class="w-4 h-4 mr-1" />
                        Editar Capa
                    </flux:button>
                </div>
            @endif

            <div class="absolute left-8 top-1/2 -translate-y-1/2 flex items-center gap-6">
                <div class="relative w-48 h-48 rounded-full border-4 border-white overflow-hidden shadow-xl group">
                    <img src="{{ $this->avatar() ?? asset('images/users/avatar.svg') }}" class="w-full h-full object-cover" />
                    <livewire:user-status-indicator :userId="$user->id" />

                    @if($this->isOwner())
                        <!-- Overlay para editar foto -->
                        <div class="absolute inset-0 bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center rounded-full">
                            <flux:button
                                href="{{ route('settings.profile-with-avatar') }}"
                                wire:navigate
                                variant="primary"
                                size="sm"
                                class="text-white border-none bg-zinc-800 shadow-lg"
                            >
                                <flux:icon.camera class="w-4 h-4 mr-1" />
                                Editar Foto
                            </flux:button>
                        </div>
                    @endif
                </div>
                <div class="bg-zinc-800 opacity-70 p-4 rounded-lg shadow-lg">
                    <h2 class="text-3xl font-bold text-white drop-shadow-lg">{{ $user->name }}</h2>
                    <a href="/{{ $user->username }}" class="text-lg text-white drop-shadow-md hover:underline">
                        {{ '@'. $user->username }}
                    </a>
                    @if($user->level)
                        <div class="text-sm font-semibold text-white drop-shadow-md mt-1">
                            Nível: {{ $user->level->level }}
                        </div>
                    @endif
                    {{-- Novo componente de status do usuário --}}
                    <livewire:user-status-manager :user="$user" />

                    @if($this->isOwner())
                        <!-- Botão para editar perfil -->
                        <div class="mt-3">
                            <flux:button
                                href="{{ route('settings.profile') }}"
                                wire:navigate
                                variant="primary"
                                size="sm"
                                class="text-white border-none bg-zinc-800 shadow-lg"
                            >
                                <flux:icon.pencil class="w-4 h-4 mr-1" />
                                Editar Perfil
                            </flux:button>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <div id="profile_navigation" class="border-t border-gray-200 dark:border-gray-700 px-3 sm:px-6 py-3 mt-4 text-sm text-body">
            <!-- Mobile: Grid layout -->
            <div class="grid grid-cols-2 sm:hidden gap-2">
                <flux:button variant="ghost" icon="photo" wire:click="showUserImages" size="sm" class="text-xs">
                    <span class="hidden xs:inline">Imagens</span>
                    <span class="xs:hidden">Img</span>
                    ({{ $this->imagesCount() }})
                </flux:button>

                <flux:button variant="ghost" icon="video-camera" wire:click="showUserVideos" size="sm" class="text-xs">
                    <span class="hidden xs:inline">Vídeos</span>
                    <span class="xs:hidden">Vid</span>
                    ({{ $this->videosCount() }})
                </flux:button>

                <flux:button variant="ghost" icon="users" wire:click="showUserFollowing" size="sm" class="text-xs">
                    <span class="hidden xs:inline">Seguindo</span>
                    <span class="xs:hidden">Seg</span>
                    {{ $this->followingCount() }}
                </flux:button>

                <flux:button variant="ghost" icon="users" wire:click="showUserFollowers" size="sm" class="text-xs">
                    <span class="hidden xs:inline">Seguidores</span>
                    <span class="xs:hidden">Segr</span>
                    {{ $this->followersCount() }}
                </flux:button>

                <flux:button variant="ghost" icon="rss" wire:click="showUserPosts" size="sm" class="text-xs">
                    <span class="hidden xs:inline">Posts</span>
                    <span class="xs:hidden">P</span>
                    {{ $this->postsCount() }}
                </flux:button>

                <flux:button variant="ghost" icon="gift" wire:click="showSendCharm" size="sm" class="text-xs">
                    <span class="hidden xs:inline">Charme</span>
                    <span class="xs:hidden">C</span>
                </flux:button>

                @if($user->id !== Auth::id())
                    <flux:button
                        wire:click="toggleFollow({{ $user->id }})"
                        variant="ghost"
                        icon="user-plus"
                        size="sm"
                        class="bg-purple-600 hover:bg-purple-700 text-white border-none text-xs col-span-1"
                    >
                        {{ $followStatus[$user->id] ? 'Parar' : 'Seguir' }}
                    </flux:button>

                    <flux:button
                        href="{{ route('caixa_de_mensagens', ['user' => $user->username]) }}"
                        wire:navigate
                        variant="ghost"
                        size="sm"
                        icon="chat-bubble-left"
                        class="text-white bg-zinc-800 hover:bg-zinc-700 border-none shadow-lg text-xs col-span-1"
                    >
                        Mensagem
                    </flux:button>
                @endif
            </div>

            <!-- Desktop: Flex layout -->
            <div class="hidden sm:flex flex-wrap items-center justify-between">
                <div class="flex flex-wrap gap-2">
                    <flux:button.group>
                        <flux:button variant="ghost" icon="photo" wire:click="showUserImages">
                            Imagens ({{ $this->imagesCount() }})
                        </flux:button>

                        <flux:button variant="ghost" icon="video-camera" wire:click="showUserVideos">
                            Vídeos ({{ $this->videosCount() }})
                        </flux:button>

                        <flux:button variant="ghost" icon="users" wire:click="showUserFollowing">
                            Seguindo: {{ $this->followingCount() }}
                        </flux:button>

                        <flux:button variant="ghost" icon="users" wire:click="showUserFollowers">
                            Seguidores: {{ $this->followersCount() }}
                        </flux:button>

                        <flux:button variant="ghost" icon="rss" wire:click="showUserPosts">
                            Postagens: {{ $this->postsCount() }}
                        </flux:button>

                        <flux:button variant="ghost" icon="gift" wire:click="showSendCharm">
                            Enviar Charme
                        </flux:button>
                    </flux:button.group>
                </div>

                @if($user->id !== Auth::id())
                    <div class="flex gap-2 mt-2 sm:mt-0">
                        <flux:button
                            wire:click="toggleFollow({{ $user->id }})"
                            variant="ghost"
                            icon="user-plus"
                            size="sm"
                            class="bg-purple-600 hover:bg-purple-700 text-white border-none"
                        >
                            {{ $followStatus[$user->id] ? 'Deixar de Seguir' : 'Seguir' }}
                        </flux:button>

                        <flux:button
                            href="{{ route('caixa_de_mensagens', ['user' => $user->username]) }}"
                            wire:navigate
                            variant="ghost"
                            size="sm"
                            icon="chat-bubble-left"
                            class="text-white bg-zinc-800 hover:bg-zinc-700 border-none shadow-lg"
                        >
                            Enviar Mensagem
                        </flux:button>
                    </div>
                @endif
            </div>
        </div>
    </div>
    <div class="flex gap-6 mt-6">
        <div class="w-1/3">
           <livewire:profile-progress-bar :username="$user->username" />
            <section id="additional-info" class="mt-6">
                <h3 class="text-lg font-semibold text-title mb-4">Informações Adicionais</h3>
                <div class="flex flex-col gap-4">
                    <div>
                        <flux:text>
                            <strong>Sexo:</strong> {{ $user->sexo ?? 'Não especificado' }}
                        </flux:text>
                        <flux:text>
                            <strong>Aniversário:</strong> {{ $user->aniversario ? $user->aniversario->format('d/m/Y') : 'Não especificado' }}
                        </flux:text>
                        <flux:text>
                            <strong>Localização:</strong> {{ $user->city->name ?? 'Não especificado' }}
                        </flux:text>
                        <flux:text>
                            <strong>Sobre mim:</strong> {{ $user->bio ?? 'Não especificado' }}
                        </flux:text>
                    </div>

                    <div>
                        <h3 class="text-lg font-semibold mb-2">Interesses:</h3>
                        <div class="flex flex-wrap gap-2">
                            @foreach($user->hobbies as $hobby)
                                <flux:badge>{{ $hobby->nome }}</flux:badge>
                            @endforeach
                        </div>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold mb-2">Procuro por:</h3>
                        <div class="flex flex-wrap gap-2">
                            @foreach($user->procuras as $procura)
                                <flux:badge>{{ $procura->nome }}</flux:badge>
                            @endforeach
                        </div>
                    </div>
                </div>
            </section>

            {{-- Seção de Conquistas --}}
            @if ($user->achievements->count() > 0)
            <section id="achievements" class="mt-6">
                <h3 class="text-lg font-semibold text-title mb-4">Conquistas</h3>
                <div class="flex flex-wrap gap-2">
                    @foreach ($user->achievements as $achievement)
                        <x-flux::badge icon="{{ $achievement->icon ?? 'star' }}" color="primary" outline>
                            {{ $achievement->name }}
                        </x-flux::badge>
                    @endforeach
                </div>
            </section>
            @endif

            <section id="ranking" class="mt-6">
                <h3 class="text-lg font-semibold text-title mb-4">Ranking</h3>
                <div class="flex flex-col gap-4">
                    @foreach($topUsers as $rank)
                        <div class="flex items-center justify-between bg-gray-100 dark:bg-gray-700 p-4 rounded-lg shadow">
                            <div class="flex items-center gap-4">
                                <div class="relative w-12 h-12 rounded-full overflow-hidden">
                                    <img src="{{ $rank->avatar ?? asset('images/default-avatar.jpg') }}" class="w-full h-full object-cover" /><livewire:user-status-indicator :userId="$rank->id" />
                                </div>
                                <div>
                                    <h4 class="text-sm font-bold text-title">{{ $rank->name }}</h4>
                                    <a href="/{{ $rank->username }}" class="text-xs text-body-light hover:text-link">{{ '@' . $rank->username }}</a>
                                </div>
                            </div>
                            <div class="text-sm font-semibold text-title">
                                {{ $rank->ranking_points }} pontos
                            </div>
                        </div>
                    @endforeach
                </div>
            </section>

            <section id="online-stats" class="mt-6">
                <livewire:user-online-stats :user="$user" />
            </section>

            {{-- GALERIA DE MÍDIA EM DESTAQUE --}}
            <section id="media-gallery-featured" class="mt-6">
                <livewire:media-gallery-featured :user="$user" />
            </section>

            {{-- Seção de Conexões Sociais --}}
            <section id="social-connections" class="mt-6">
                <livewire:social-connections :user="$user" />
            </section>

            {{-- Seção de Visualização da Rede --}}
            <section id="network-visualization" class="mt-6">
                <livewire:network-visualization :user="$user" />
            </section>

            {{-- Estatísticas da Galeria --}}
            <section id="media-stats" class="mt-6">
                <livewire:media-stats :user="$user" />
            </section>

            {{-- Seção de Galeria de Mídia Completa --}}
            <section id="media-gallery" class="mt-6">
                <livewire:media-gallery :user="$user" />
            </section>
        </div>
        <div class="w-2/3">
            <section id="create-post">
                <livewire:create-post />
                <livewire:postfeed />
            </section>

        </div>
    </div>

    {{-- Botão Flutuante para Galeria (apenas para o próprio usuário) --}}
    @if(Auth::check() && Auth::id() === $user->id)
        <div class="fixed bottom-6 right-6 z-40">
            <div class="relative group">
                {{-- Botão Principal --}}
                <button onclick="document.getElementById('media-gallery-featured').scrollIntoView({ behavior: 'smooth' })"
                        class="w-14 h-14 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center group-hover:scale-110">
                    <flux:icon.photo class="w-6 h-6" />
                </button>

                {{-- Tooltip --}}
                <div class="absolute bottom-full right-0 mb-2 px-3 py-2 bg-black text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                    Ir para Galeria
                    <div class="absolute top-full right-4 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-black"></div>
                </div>
            </div>
        </div>
    @endif
</div>

