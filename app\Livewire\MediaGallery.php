<?php

namespace App\Livewire;

use Livewire\Component;
use Livewire\WithFileUploads;
use App\Models\User;
use App\Models\Album;
use App\Models\AlbumMedia;

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;


class MediaGallery extends Component
{
    use WithFileUploads;

    public User $user;
    public $albums = [];
    public $selectedAlbum = null;
    public $medias = [];
    public $currentView = 'grid'; // grid, list, album
    public $selectedMedia = null;
    public $showModal = false;
    public $showUploadModal = false;
    public $showCreateAlbumModal = false;
    public $filterType = 'all'; // all, photos, videos

    // Upload
    public $uploadFiles = [];
    public $uploadAlbumId = null;
    public $uploadTitle = '';
    public $uploadDescription = '';

    // Criar álbum
    public $newAlbumName = '';
    public $newAlbumDescription = '';
    public $newAlbumPrivacy = 'public';

    public function mount(User $user)
    {
        $this->user = $user;
        $this->loadAlbums();
        $this->loadMedias();
    }

    public function loadAlbums()
    {
        $query = $this->user->albums()->with(['medias' => function ($q) {
            $q->take(4);
        }]);

        // Se não é o próprio usuário, mostrar apenas álbuns públicos
        if (!Auth::check() || Auth::id() !== $this->user->id) {
            $query->where('privacy', 'public');
        }

        $this->albums = $query->get();

        // Criar álbum padrão se não existir
        if ($this->albums->isEmpty() && Auth::check() && Auth::id() === $this->user->id) {
            $this->createDefaultAlbum();
        }
    }

    public function loadMedias()
    {
        $query = AlbumMedia::where('user_id', $this->user->id)
            ->with('album');

        // Filtrar por álbum selecionado
        if ($this->selectedAlbum) {
            $query->where('album_id', $this->selectedAlbum);
        }

        // Filtrar por tipo
        if ($this->filterType !== 'all') {
            $type = $this->filterType === 'photos' ? 'photo' : 'video';
            $query->where('type', $type);
        }

        // Se não é o próprio usuário, mostrar apenas mídias de álbuns públicos
        if (!Auth::check() || Auth::id() !== $this->user->id) {
            $query->whereHas('album', function ($q) {
                $q->where('privacy', 'public');
            });
        }

        $this->medias = $query->ordered()->get();
    }

    public function setView($view)
    {
        $this->currentView = $view;
    }

    public function setFilter($filter)
    {
        $this->filterType = $filter;
        $this->loadMedias();
    }

    public function selectAlbum($albumId)
    {
        $this->selectedAlbum = $albumId;
        $this->loadMedias();
    }

    public function showMedia($mediaId)
    {
        $this->selectedMedia = AlbumMedia::with('album')->find($mediaId);
        $this->showModal = true;
    }

    public function closeModal()
    {
        $this->showModal = false;
        $this->selectedMedia = null;
    }

    public function openUploadModal()
    {
        if (!Auth::check() || Auth::id() !== $this->user->id) {
            return;
        }

        $this->showUploadModal = true;
        $this->uploadAlbumId = $this->albums->first()?->id;
    }

    public function closeUploadModal()
    {
        $this->showUploadModal = false;
        $this->uploadFiles = [];
        $this->uploadTitle = '';
        $this->uploadDescription = '';
    }

    public function openCreateAlbumModal()
    {
        if (!Auth::check() || Auth::id() !== $this->user->id) {
            return;
        }

        $this->showCreateAlbumModal = true;
    }

    public function closeCreateAlbumModal()
    {
        $this->showCreateAlbumModal = false;
        $this->newAlbumName = '';
        $this->newAlbumDescription = '';
        $this->newAlbumPrivacy = 'public';
    }

    public function createAlbum()
    {
        $this->validate([
            'newAlbumName' => 'required|string|max:255',
            'newAlbumDescription' => 'nullable|string|max:1000',
            'newAlbumPrivacy' => 'required|in:public,friends,private',
        ]);

        $album = Album::create([
            'user_id' => Auth::id(),
            'name' => $this->newAlbumName,
            'description' => $this->newAlbumDescription,
            'privacy' => $this->newAlbumPrivacy,
            'sort_order' => $this->albums->count(),
        ]);

        $this->loadAlbums();
        $this->closeCreateAlbumModal();

        $this->dispatch(
            'toast',
            message: 'Álbum criado com sucesso!',
            type: 'success'
        )->to('toast-notification');
    }

    public function uploadMedia()
    {
        $this->validate([
            'uploadFiles.*' => 'required|file|mimes:jpeg,png,jpg,gif,mp4,mov,avi|max:20480', // 20MB
            'uploadAlbumId' => 'required|exists:albums,id',
        ]);

        $album = Album::find($this->uploadAlbumId);

        if (!$album || $album->user_id !== Auth::id()) {
            return;
        }

        foreach ($this->uploadFiles as $file) {
            $this->processUpload($file, $album);
        }

        $this->loadMedias();
        $this->closeUploadModal();

        $this->dispatch(
            'toast',
            message: count($this->uploadFiles) . ' arquivo(s) enviado(s) com sucesso!',
            type: 'success'
        )->to('toast-notification');
    }

    protected function processUpload($file, $album)
    {
        $isVideo = in_array($file->getClientOriginalExtension(), ['mp4', 'mov', 'avi']);
        $type = $isVideo ? 'video' : 'photo';

        // Salvar arquivo
        $path = $file->store('albums/' . $album->id, 'public');

        // Criar thumbnail para fotos
        $thumbnailPath = null;
        $width = null;
        $height = null;

        if (!$isVideo) {
            try {
                $fullPath = storage_path('app/public/' . $path);

                // Obter dimensões da imagem
                $imageInfo = getimagesize($fullPath);
                if ($imageInfo) {
                    $width = $imageInfo[0];
                    $height = $imageInfo[1];

                    // Criar thumbnail usando GD
                    $thumbnailPath = $this->createThumbnail($fullPath, $album->id, basename($path));
                }
            } catch (\Exception $e) {
                // Se falhar, usar a imagem original
                logger()->warning('Erro ao processar imagem: ' . $e->getMessage());
            }
        }

        AlbumMedia::create([
            'album_id' => $album->id,
            'user_id' => Auth::id(),
            'type' => $type,
            'file_path' => $path,
            'thumbnail_path' => $thumbnailPath,
            'title' => $this->uploadTitle,
            'description' => $this->uploadDescription,
            'file_size' => $file->getSize(),
            'mime_type' => $file->getMimeType(),
            'width' => $width,
            'height' => $height,
        ]);
    }

    protected function createThumbnail($sourcePath, $albumId, $filename)
    {
        try {
            // Verificar se GD está disponível
            if (!extension_loaded('gd')) {
                return null;
            }

            // Criar diretório de thumbnails se não existir
            $thumbDir = storage_path('app/public/albums/' . $albumId . '/thumbs');
            if (!file_exists($thumbDir)) {
                mkdir($thumbDir, 0755, true);
            }

            $thumbnailPath = 'albums/' . $albumId . '/thumbs/' . $filename;
            $thumbnailFullPath = storage_path('app/public/' . $thumbnailPath);

            // Obter informações da imagem
            $imageInfo = getimagesize($sourcePath);
            if (!$imageInfo) {
                return null;
            }

            $originalWidth = $imageInfo[0];
            $originalHeight = $imageInfo[1];
            $imageType = $imageInfo[2];

            // Criar resource da imagem original
            $sourceImage = null;
            switch ($imageType) {
                case IMAGETYPE_JPEG:
                    $sourceImage = imagecreatefromjpeg($sourcePath);
                    break;
                case IMAGETYPE_PNG:
                    $sourceImage = imagecreatefrompng($sourcePath);
                    break;
                case IMAGETYPE_GIF:
                    $sourceImage = imagecreatefromgif($sourcePath);
                    break;
                default:
                    return null;
            }

            if (!$sourceImage) {
                return null;
            }

            // Calcular dimensões do thumbnail (300x300 max, mantendo proporção)
            $thumbSize = 300;
            $ratio = min($thumbSize / $originalWidth, $thumbSize / $originalHeight);
            $thumbWidth = intval($originalWidth * $ratio);
            $thumbHeight = intval($originalHeight * $ratio);

            // Criar thumbnail
            $thumbnail = imagecreatetruecolor($thumbWidth, $thumbHeight);

            // Preservar transparência para PNG
            if ($imageType == IMAGETYPE_PNG) {
                imagealphablending($thumbnail, false);
                imagesavealpha($thumbnail, true);
                $transparent = imagecolorallocatealpha($thumbnail, 255, 255, 255, 127);
                imagefill($thumbnail, 0, 0, $transparent);
            }

            // Redimensionar
            imagecopyresampled(
                $thumbnail,
                $sourceImage,
                0,
                0,
                0,
                0,
                $thumbWidth,
                $thumbHeight,
                $originalWidth,
                $originalHeight
            );

            // Salvar thumbnail
            $success = false;
            switch ($imageType) {
                case IMAGETYPE_JPEG:
                    $success = imagejpeg($thumbnail, $thumbnailFullPath, 85);
                    break;
                case IMAGETYPE_PNG:
                    $success = imagepng($thumbnail, $thumbnailFullPath, 8);
                    break;
                case IMAGETYPE_GIF:
                    $success = imagegif($thumbnail, $thumbnailFullPath);
                    break;
            }

            // Limpar memória
            imagedestroy($sourceImage);
            imagedestroy($thumbnail);

            return $success ? $thumbnailPath : null;
        } catch (\Exception $e) {
            logger()->warning('Erro ao criar thumbnail: ' . $e->getMessage());
            return null;
        }
    }

    protected function createDefaultAlbum()
    {
        Album::create([
            'user_id' => $this->user->id,
            'name' => 'Fotos do Perfil',
            'description' => 'Álbum padrão para fotos do perfil',
            'privacy' => 'public',
            'is_default' => true,
            'sort_order' => 0,
        ]);

        $this->loadAlbums();
    }

    public function deleteMedia($mediaId)
    {
        if (!Auth::check() || Auth::id() !== $this->user->id) {
            return;
        }

        $media = AlbumMedia::find($mediaId);
        if ($media && $media->user_id === Auth::id()) {
            // Deletar arquivos
            Storage::disk('public')->delete($media->file_path);
            if ($media->thumbnail_path) {
                Storage::disk('public')->delete($media->thumbnail_path);
            }

            $media->delete();
            $this->loadMedias();

            $this->dispatch(
                'toast',
                message: 'Mídia deletada com sucesso!',
                type: 'success'
            )->to('toast-notification');
        }
    }

    public function render()
    {
        return view('livewire.media-gallery');
    }
}
