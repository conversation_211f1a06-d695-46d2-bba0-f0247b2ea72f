<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Livewire\Livewire;
use App\Livewire\CreatePost;
use App\Livewire\ProfileComponent;
use App\Livewire\UserImages;
use App\Livewire\UserVideos;
use App\Livewire\UserFollowing;
use App\Livewire\UserFollowers;
use App\Livewire\UserPosts;
use App\Livewire\SendCharm;
use App\Livewire\Leaderboard;
use App\Livewire\SearchModal;
use App\Livewire\ToastNotification;
use App\Livewire\MessageNotifier;

class AppServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        //
    }

    public function boot(): void
    {
        Livewire::component('create-post', CreatePost::class);
        Livewire::component('profile-component', ProfileComponent::class);
        Livewire::component('settings.profile-form', \App\Livewire\Settings\ProfileForm::class);

        // Componentes de perfil
        Livewire::component('user-images', UserImages::class);
        Livewire::component('user-videos', UserVideos::class);
        Livewire::component('user-following', UserFollowing::class);
        Livewire::component('user-followers', UserFollowers::class);
        Livewire::component('user-posts', UserPosts::class);
        Livewire::component('send-charm', SendCharm::class);
        Livewire::component('leaderboard', Leaderboard::class);
        Livewire::component('social-connections', \App\Livewire\SocialConnections::class);
        Livewire::component('smart-suggestions', \App\Livewire\SmartSuggestions::class);
        Livewire::component('network-visualization', \App\Livewire\NetworkVisualization::class);
        Livewire::component('media-gallery', \App\Livewire\MediaGallery::class);
        Livewire::component('media-preview', \App\Livewire\MediaPreview::class);
        Livewire::component('media-gallery-featured', \App\Livewire\MediaGalleryFeatured::class);
        Livewire::component('media-stats', \App\Livewire\MediaStats::class);

        // Componentes de grupos
        Livewire::component('groups.create-group', \App\Livewire\Groups\CreateGroup::class);
        Livewire::component('groups.group-detail', \App\Livewire\Groups\GroupDetail::class);
        Livewire::component('groups.group-members', \App\Livewire\Groups\GroupMembers::class);
        Livewire::component('groups.group-posts', \App\Livewire\Groups\GroupPosts::class);
        Livewire::component('groups.group-invitations', \App\Livewire\Groups\GroupInvitations::class);
        Livewire::component('groups.group-list', \App\Livewire\Groups\GroupList::class);

        // Componentes de loja
        Livewire::component('shop.mini-cart', \App\Livewire\Shop\MiniCart::class);

        // Componente de busca
        Livewire::component('search-modal', SearchModal::class);

        // Componentes de suporte
        Livewire::component('support.create-ticket-modal', \App\Livewire\Support\CreateTicketModal::class);
        Livewire::component('support.edit-ticket', \App\Livewire\Support\EditTicket::class);

        // Componente de mensagens
        Livewire::component('messages', \App\Livewire\Messages::class);

        // Componente de notificações toast
        Livewire::component('toast-notification', ToastNotification::class);

        // Componente de notificação de mensagens
        Livewire::component('message-notifier', MessageNotifier::class);

        // Componentes do Monte Sua Noite
        Livewire::component('night-board.builder', \App\Livewire\NightBoard\Builder::class);
        Livewire::component('night-board.show-plan', \App\Livewire\NightBoard\ShowPlan::class);
        Livewire::component('night-board.plan-list', \App\Livewire\NightBoard\PlanList::class);

        // Componentes de Suporte
        Livewire::component('support.help-center', \App\Livewire\Support\HelpCenter::class);
        Livewire::component('support.create-ticket', \App\Livewire\Support\CreateTicket::class);
        Livewire::component('support.ticket-view', \App\Livewire\Support\TicketView::class);
        Livewire::component('support.article-feedback', \App\Livewire\Support\ArticleFeedback::class);

        // Componentes Admin
        Livewire::component('admin.support-tickets', \App\Livewire\Admin\SupportTickets::class);
    }
}
