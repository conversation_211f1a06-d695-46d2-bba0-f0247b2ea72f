/**
 * Night Board - Monte Sua Noite
 * Estilos específicos para a funcionalidade de tabuleiro interativo
 */

/* Cores neon específicas do Night Board */
:root {
  --neon-pink: #E60073;
  --neon-cyan: #00FFF7;
  --neon-yellow: #FFE600;
  --neon-orange: #FF6B35;
  --neon-purple: #9D4EDD;
  --neon-green: #06FFA5;
}

/* Efeitos de hover para blocos temáticos */
.night-board-block {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
}

.night-board-block:hover {
  transform: scale(1.05);
  box-shadow: 0 0 20px rgba(230, 0, 115, 0.4);
}

.night-board-block.selected {
  transform: scale(1.1);
  box-shadow: 0 0 25px rgba(230, 0, 115, 0.6);
}

/* Animações para o tabuleiro */
.chess-square {
  transition: all 0.2s ease-in-out;
  position: relative;
  overflow: hidden;
}

.chess-square::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
  transform: translateX(-100%);
  transition: transform 0.6s;
}

.chess-square:hover::before {
  transform: translateX(100%);
}

.chess-square:hover {
  transform: scale(1.05);
  z-index: 10;
}

/* Efeitos para casas disponíveis para patrocínio */
.sponsor-available {
  background: linear-gradient(135deg, #f3f4f6, #e5e7eb);
  border: 2px dashed #06FFA5;
  animation: pulse-green 2s infinite;
}

.sponsor-available:hover {
  background: linear-gradient(135deg, #ecfdf5, #d1fae5);
  border-color: #10b981;
  box-shadow: 0 0 15px rgba(6, 255, 165, 0.3);
}

@keyframes pulse-green {

  0%,
  100% {
    border-color: #06FFA5;
    box-shadow: 0 0 0 0 rgba(6, 255, 165, 0.4);
  }

  50% {
    border-color: #10b981;
    box-shadow: 0 0 0 8px rgba(6, 255, 165, 0);
  }
}

/* Efeitos para casas patrocinadas */
.sponsored-square {
  background: linear-gradient(135deg, #fef3c7, #fde68a);
  border: 2px solid #f59e0b;
  box-shadow: 0 0 10px rgba(245, 158, 11, 0.3);
}

.sponsored-square:hover {
  box-shadow: 0 0 20px rgba(245, 158, 11, 0.5);
}

/* Animações para ícones dos blocos */
.block-icon {
  transition: all 0.3s ease;
  filter: drop-shadow(0 0 5px currentColor);
}

.block-icon:hover {
  transform: scale(1.2) rotate(5deg);
  filter: drop-shadow(0 0 10px currentColor);
}

/* Efeitos de drag and drop */
.night-board-block[draggable="true"] {
  cursor: grab;
  user-select: none;
}

.night-board-block[draggable="true"]:active {
  cursor: grabbing;
}

.night-board-block[draggable="true"]:hover {
  transform: scale(1.05);
  box-shadow: 0 0 20px rgba(230, 0, 115, 0.4);
}

/* Estado durante o drag */
.dragging {
  opacity: 0.5;
  transform: rotate(5deg) scale(0.95);
  z-index: 1000;
}

/* Área de drop válida */
.drag-over {
  background: linear-gradient(135deg, rgba(230, 0, 115, 0.15), rgba(230, 0, 115, 0.25)) !important;
  border: 2px dashed #E60073 !important;
  animation: drag-pulse 1s infinite;
  transform: scale(1.05);
  z-index: 10;
}

/* Área de drop inválida */
.drag-invalid {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(239, 68, 68, 0.2)) !important;
  border: 2px dashed #ef4444 !important;
  animation: shake 0.5s ease-in-out;
}

@keyframes drag-pulse {

  0%,
  100% {
    box-shadow: 0 0 0 0 rgba(230, 0, 115, 0.4);
  }

  50% {
    box-shadow: 0 0 0 8px rgba(230, 0, 115, 0);
  }
}

@keyframes shake {

  0%,
  100% {
    transform: translateX(0);
  }

  25% {
    transform: translateX(-2px);
  }

  75% {
    transform: translateX(2px);
  }
}

@keyframes bounce {

  0%,
  20%,
  53%,
  80%,
  100% {
    transform: translate3d(0, 0, 0);
  }

  40%,
  43% {
    transform: translate3d(0, -8px, 0);
  }

  70% {
    transform: translate3d(0, -4px, 0);
  }

  90% {
    transform: translate3d(0, -2px, 0);
  }
}

/* Feedback visual para blocos sendo arrastados */
.block-ghost {
  position: fixed;
  pointer-events: none;
  z-index: 9999;
  opacity: 0.8;
  transform: rotate(5deg) scale(0.9);
  transition: none;
}

/* Efeitos para preview do tabuleiro na listagem */
.board-preview {
  transition: all 0.3s ease;
}

.board-preview:hover {
  transform: scale(1.1);
  filter: brightness(1.2);
}

/* Animação de entrada para modais */
.modal-enter {
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }

  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Efeitos para botões de ação */
.night-board-btn {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.night-board-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.night-board-btn:hover::before {
  left: 100%;
}

/* Efeitos para estatísticas */
.stat-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
}

/* Responsividade para mobile */
@media (max-width: 768px) {
  .chess-square {
    min-height: 40px;
  }

  .block-icon {
    width: 16px;
    height: 16px;
  }

  .night-board-block {
    padding: 8px;
  }
}

/* Dark mode específico */
.dark .chess-square {
  border-color: rgba(255, 255, 255, 0.1);
}

.dark .sponsor-available {
  background: linear-gradient(135deg, #374151, #4b5563);
  border-color: #06FFA5;
}

.dark .sponsored-square {
  background: linear-gradient(135deg, #451a03, #78350f);
  border-color: #f59e0b;
}

.dark .stat-card {
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.1));
  border-color: rgba(255, 255, 255, 0.1);
}

/* Animação de loading para o tabuleiro */
.board-loading {
  animation: boardShimmer 1.5s infinite;
}

@keyframes boardShimmer {
  0% {
    background-position: -200px 0;
  }

  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.board-loading {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
}

.dark .board-loading {
  background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
  background-size: 200px 100%;
}

/* Estilos para texto personalizado nos blocos */
.custom-text-indicator {
  background: rgba(0, 255, 247, 0.1);
  border: 1px solid rgba(0, 255, 247, 0.3);
  border-radius: 4px;
  padding: 2px 4px;
  margin-top: 2px;
  font-size: 9px;
  line-height: 1.2;
  transition: all 0.2s ease;
  cursor: pointer;
}

.custom-text-indicator:hover {
  background: rgba(0, 255, 247, 0.2);
  border-color: rgba(0, 255, 247, 0.5);
  transform: scale(1.05);
  z-index: 20;
}

.custom-text-placeholder {
  color: rgba(156, 163, 175, 0.6);
  font-style: italic;
  font-size: 9px;
  margin-top: 2px;
  transition: opacity 0.2s ease;
}

.chess-square:hover .custom-text-placeholder {
  opacity: 1;
}

/* Animação para texto sendo editado */
.text-editing {
  animation: textPulse 1.5s infinite;
}

@keyframes textPulse {

  0%,
  100% {
    background-color: rgba(230, 0, 115, 0.1);
  }

  50% {
    background-color: rgba(230, 0, 115, 0.2);
  }
}

/* Responsividade para texto personalizado */
@media (max-width: 768px) {
  .custom-text-indicator {
    font-size: 7px;
    padding: 1px 2px;
  }

  .custom-text-placeholder {
    font-size: 7px;
  }
}