<div class="space-y-6 overflow-hidden">
    @if(Auth::check() && Auth::id() !== $user->id)


        {{-- Con<PERSON>õ<PERSON> --}}
        @if($mutualFollowers->count() > 0 || $mutualFollowing->count() > 0)
        <section class="bg-white dark:bg-zinc-800 rounded-lg shadow-md p-6 overflow-hidden">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                <flux:icon.heart class="w-5 h-5 mr-2 text-red-500" />
                Conexões em Comum
            </h3>
            
            {{-- Seguidores Mútu<PERSON> --}}
            @if($mutualFollowers->count() > 0)
            <div class="mb-6">
                <div class="flex items-center justify-between mb-3">
                    <h4 class="font-medium text-gray-900 dark:text-white">
                        Seguidores em comum ({{ $networkStats['mutual_followers_count'] }})
                    </h4>
                    @if($networkStats['mutual_followers_count'] > 6)
                        <flux:button 
                            wire:click="toggleMutualFollowers" 
                            variant="ghost" 
                            size="sm"
                            class="text-purple-600 hover:text-purple-700"
                        >
                            {{ $showMutualFollowers ? 'Ver menos' : 'Ver todos' }}
                        </flux:button>
                    @endif
                </div>
                
                <div class="flex flex-wrap gap-3">
                    @foreach($mutualFollowers->take($showMutualFollowers ? 20 : 6) as $follower)
                        <div class="flex flex-col items-center group w-16">
                            <a href="/{{ $follower->username }}" wire:navigate class="relative">
                                <img
                                    src="{{ $this->getAvatar($follower) }}"
                                    alt="{{ $follower->name }}"
                                    class="w-12 h-12 rounded-full border-2 border-gray-200 dark:border-gray-600 group-hover:border-purple-500 transition-colors"
                                >
                                <livewire:user-status-indicator :userId="$follower->id" :key="'status-'.$follower->id" />
                            </a>
                            <span class="text-xs text-gray-600 dark:text-gray-400 mt-1 text-center w-full truncate" title="{{ $follower->name }}">
                                {{ $follower->name }}
                            </span>
                        </div>
                    @endforeach
                </div>
            </div>
            @endif

            {{-- Seguindo Mútuos --}}
            @if($mutualFollowing->count() > 0)
            <div class="mb-4">
                <div class="flex items-center justify-between mb-3">
                    <h4 class="font-medium text-gray-900 dark:text-white">
                        Seguindo em comum ({{ $networkStats['mutual_following_count'] }})
                    </h4>
                    @if($networkStats['mutual_following_count'] > 6)
                        <flux:button 
                            wire:click="toggleMutualFollowing" 
                            variant="ghost" 
                            size="sm"
                            class="text-blue-600 hover:text-blue-700"
                        >
                            {{ $showMutualFollowing ? 'Ver menos' : 'Ver todos' }}
                        </flux:button>
                    @endif
                </div>
                
                <div class="flex flex-wrap gap-3">
                    @foreach($mutualFollowing->take($showMutualFollowing ? 20 : 6) as $following)
                        <div class="flex flex-col items-center group w-16">
                            <a href="/{{ $following->username }}" wire:navigate class="relative">
                                <img
                                    src="{{ $this->getAvatar($following) }}"
                                    alt="{{ $following->name }}"
                                    class="w-12 h-12 rounded-full border-2 border-gray-200 dark:border-gray-600 group-hover:border-blue-500 transition-colors"
                                >
                                <livewire:user-status-indicator :userId="$following->id" :key="'status-'.$following->id" />
                            </a>
                            <span class="text-xs text-gray-600 dark:text-gray-400 mt-1 text-center w-full truncate" title="{{ $following->name }}">
                                {{ $following->name }}
                            </span>
                        </div>
                    @endforeach
                </div>
            </div>
            @endif
        </section>
        @endif

        {{-- Grupos em Comum --}}
        @if($commonGroups->count() > 0)
        <section class="bg-white dark:bg-zinc-800 rounded-lg shadow-md p-6 overflow-hidden">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                <flux:icon.user-group class="w-5 h-5 mr-2 text-green-500" />
                Grupos em Comum ({{ $networkStats['common_groups_count'] }})
            </h3>
            
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 overflow-hidden">
                @foreach($commonGroups->take($showCommonGroups ? 20 : 6) as $group)
                    <div class="flex items-center p-3 bg-gray-50 dark:bg-zinc-700 rounded-lg hover:bg-gray-100 dark:hover:bg-zinc-600 transition-colors min-w-0">
                        <div class="w-10 h-10 bg-gradient-to-br from-green-400 to-green-600 rounded-lg flex items-center justify-center mr-3 flex-shrink-0">
                            <flux:icon.user-group class="w-5 h-5 text-white" />
                        </div>
                        <div class="flex-1 min-w-0 overflow-hidden">
                            <a href="{{ route('grupos.show', $group->slug) }}" wire:navigate class="font-medium text-gray-900 dark:text-white hover:text-green-600 dark:hover:text-green-400 transition-colors block truncate" title="{{ $group->name }}">
                                {{ $group->name }}
                            </a>
                            <p class="text-sm text-gray-600 dark:text-gray-400 truncate">
                                {{ $group->members_count }} membros
                            </p>
                        </div>
                    </div>
                @endforeach
            </div>
            
            @if($networkStats['common_groups_count'] > 6)
                <div class="mt-4 text-center">
                    <flux:button 
                        wire:click="toggleCommonGroups" 
                        variant="ghost" 
                        size="sm"
                        class="text-green-600 hover:text-green-700"
                    >
                        {{ $showCommonGroups ? 'Ver menos' : 'Ver todos os grupos' }}
                    </flux:button>
                </div>
            @endif
        </section>
        @endif

        {{-- Sugestões de Conexão --}}
        @if($suggestedConnections->count() > 0)
        <section class="bg-white dark:bg-zinc-800 rounded-lg shadow-md p-6 overflow-hidden">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                <flux:icon.sparkles class="w-5 h-5 mr-2 text-yellow-500" />
                Pessoas que você pode conhecer
            </h3>
            
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 overflow-hidden">
                @foreach($suggestedConnections->take($showSuggestedConnections ? 20 : 6) as $suggestion)
                    <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-zinc-700 rounded-lg min-w-0">
                        <div class="flex items-center min-w-0 flex-1 mr-3">
                            <a href="/{{ $suggestion->username }}" wire:navigate class="relative mr-3 flex-shrink-0">
                                <img
                                    src="{{ $this->getAvatar($suggestion) }}"
                                    alt="{{ $suggestion->name }}"
                                    class="w-10 h-10 rounded-full border-2 border-gray-200 dark:border-gray-600"
                                >
                                <livewire:user-status-indicator :userId="$suggestion->id" :key="'status-'.$suggestion->id" />
                            </a>
                            <div class="min-w-0 flex-1 overflow-hidden">
                                <a href="/{{ $suggestion->username }}" wire:navigate class="font-medium text-gray-900 dark:text-white hover:text-purple-600 dark:hover:text-purple-400 transition-colors block truncate" title="{{ $suggestion->name }}">
                                    {{ $suggestion->name }}
                                </a>
                                <p class="text-sm text-gray-600 dark:text-gray-400 truncate">
                                    {{ '@' . $suggestion->username }}
                                </p>
                            </div>
                        </div>
                        <flux:button
                            wire:click="followUser({{ $suggestion->id }})"
                            variant="primary"
                            size="sm"
                            class="bg-purple-600 hover:bg-purple-700 text-white border-none flex-shrink-0"
                        >
                            <flux:icon.plus class="w-4 h-4 mr-1" />
                            Seguir
                        </flux:button>
                    </div>
                @endforeach
            </div>
            
            @if($suggestedConnections->count() > 6)
                <div class="mt-4 text-center">
                    <flux:button 
                        wire:click="toggleSuggestedConnections" 
                        variant="ghost" 
                        size="sm"
                        class="text-yellow-600 hover:text-yellow-700"
                    >
                        {{ $showSuggestedConnections ? 'Ver menos' : 'Ver mais sugestões' }}
                    </flux:button>
                </div>
            @endif
        </section>
        @endif

        {{-- Mensagem quando não há conexões --}}
        @if($mutualFollowers->count() === 0 && $mutualFollowing->count() === 0 && $commonGroups->count() === 0 && $suggestedConnections->count() === 0)
        <section class="bg-white dark:bg-zinc-800 rounded-lg shadow-md p-6 text-center overflow-hidden">
            <flux:icon.users class="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
                Nenhuma conexão em comum
            </h3>
            <p class="text-gray-600 dark:text-gray-400">
                Vocês ainda não têm seguidores, grupos ou conexões em comum.
            </p>
        </section>
        @endif
    @endif
</div>
