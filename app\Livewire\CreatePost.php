<?php

namespace App\Livewire;

use Livewire\Component;
use Livewire\WithFileUploads;
use App\Models\Post;
use App\Models\UserPoint;
use App\Models\UserPointLog;
use App\Services\ContentProcessor;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Http; // Added for API calls

class CreatePost extends Component
{
    use WithFileUploads;

    public $content = '';
    public $image = null;
    public $video = null;
    public $uploading = false;

    // Properties for hashtag autocomplete
    public $hashtagQuery = '';
    public $hashtagSuggestions = [];
    public $showHashtagSuggestions = false;
    protected $currentHashtagStartPosition = null;

    // Properties for mention autocomplete
    public $mentionQuery = '';
    public $mentionSuggestions = [];
    public $showMentionSuggestions = false;
    protected $currentMentionStartPosition = null;

    protected function rules()
    {
        return [
            'content' => $this->image || $this->video ? 'nullable' : 'required|min:3',
            'image' => 'nullable|image|max:10240', // 10MB
            'video' => 'nullable|file|mimetypes:video/mp4,video/quicktime|max:102400' // 100MB
        ];
    }

    protected $messages = [
        'video.max' => 'O vídeo não pode ser maior que 100MB.',
        'video.mimetypes' => 'O vídeo deve estar no formato MP4 ou MOV.',
    ];

    public function store()
    {
        $this->uploading = true;
        try {
            $this->validate();

            // Ensure at least one of content, image, or video is present
            if (empty($this->content) && !$this->image && !$this->video) {
                session()->flash('error', 'É necessário fornecer pelo menos um conteúdo, imagem ou vídeo.');
                $this->uploading = false;
                return;
            }

            $imagePath = null;
            if ($this->image) {
                try {
                    $filename = time() . '_' . $this->image->getClientOriginalName();
                    $imagePath = $this->image->storeAs('posts/images', $filename, 'public');
                    if (!$imagePath) {
                        throw new \Exception('Falha ao salvar a imagem');
                    }
                } catch (\Exception $e) {
                    Log::error('Error uploading image: ' . $e->getMessage());
                    throw new \Exception('Erro ao fazer upload da imagem: ' . $e->getMessage());
                }
            }

            $videoPath = null;
            if ($this->video) {
                try {
                    $videoFilename = time() . '_' . $this->video->getClientOriginalName();
                    $videoPath = $this->video->storeAs('posts/videos', $videoFilename, 'public');
                    if (!$videoPath) {
                        throw new \Exception('Falha ao salvar o vídeo');
                    }
                } catch (\Exception $e) {
                    Log::error('Error uploading video: ' . $e->getMessage());
                    if ($imagePath) {
                        Storage::disk('public')->delete($imagePath);
                    }
                    throw new \Exception('Erro ao fazer upload do vídeo: ' . $e->getMessage());
                }
            }

            $post = Post::create([
                'content' => $this->content,
                'image' => $imagePath,
                'video' => $videoPath,
                'user_id' => auth()->id(),
                'group_id' => null, // Definindo explicitamente como null para posts fora de grupos
            ]);

            if ($post) {
                Log::info('Post created successfully', ['post' => $post->toArray()]);

                // Processar menções e hashtags
                ContentProcessor::processMentions($this->content, $post, auth()->id());
                ContentProcessor::processHashtags($this->content, $post);

                $user = auth()->user();
                $pointsToAdd = 10; // Base points

                // Bônus por conteúdo multimídia
                if ($imagePath) $pointsToAdd += 5;
                if ($videoPath) $pointsToAdd += 10;

                // Bônus por tamanho do conteúdo
                if (strlen($this->content) > 100) $pointsToAdd += 5;

                // Adiciona pontos ao usuário
                \App\Models\UserPoint::addPoints(
                    $user->id,
                    'post',
                    $pointsToAdd,
                    "Criou uma nova postagem" .
                    ($imagePath ? " com imagem" : "") .
                    ($videoPath ? " com vídeo" : ""),
                    $post->id,
                    \App\Models\Post::class
                );

                // Dispara evento para animação de recompensa
                $this->dispatch('reward-earned', points: $pointsToAdd);

                // Lógica para conceder a conquista "Primeiro Post"
                $user = auth()->user();
                if ($user && $user->posts()->count() === 1) { // Verifica se este é o primeiro post do usuário
                    $firstPostAchievement = \App\Models\Achievement::where('name', 'Primeiro Post')->first();
                    if ($firstPostAchievement && !$user->achievements->contains($firstPostAchievement->id)) {
                        // Concede a conquista e adiciona os pontos da conquista ao usuário
                        $user->achievements()->attach($firstPostAchievement->id);

                         // Adicionar os pontos da conquista aos pontos totais do usuário
                        \App\Models\UserPoint::addPoints(
                            $user->id,
                            'achievement',
                            $firstPostAchievement->points,
                            "Ganhou a conquista \"" . $firstPostAchievement->name . "\"",
                            $firstPostAchievement->id,
                            \App\Models\Achievement::class
                        );

                        session()->flash('achievement', $firstPostAchievement->name); // Opcional: para mostrar uma notificação de conquista
                    }
                }

                $this->reset(['content', 'image', 'video']);
                session()->flash('message', 'Post criado com sucesso!');
                return redirect()->route('dashboard');
            }
        } catch (\Exception $e) {
            Log::error('Error creating post: ' . $e->getMessage());
            session()->flash('error', 'Erro ao criar o post: ' . $e->getMessage());
        } finally {
            $this->uploading = false;
        }
    }

    public function render()
    {
        return view('livewire.create-post');
    }

    public function handleContentInput()
    {
        $content = $this->content;
        // Reset suggestions visibility initially
        $this->showHashtagSuggestions = false;
        $this->showMentionSuggestions = false;

        // Check for Hashtag: #tag at the end of a word or content
        if (preg_match('/(?<=\s|^)\#([a-zA-Z0-9_]*)$/', $content, $matches, PREG_OFFSET_CAPTURE)) {
            $this->currentHashtagStartPosition = $matches[0][1];
            $this->hashtagQuery = $matches[1][0]; // Triggers updatedHashtagQuery
            // Hide mention suggestions if we're clearly typing a hashtag
            $this->mentionQuery = '';
            $this->mentionSuggestions = [];
            return;
        } else {
            // If no hashtag pattern is at the end, reset hashtag query
            if (!empty($this->hashtagQuery)) {
                 $this->hashtagQuery = '';
                 $this->hashtagSuggestions = [];
            }
        }

        // Check for Mention: @user at the end of a word or content
        // Allows for usernames with dots (e.g. @john.doe)
        if (preg_match('/(?<=\s|^)\@([a-zA-Z0-9_.]*)$/', $content, $matches, PREG_OFFSET_CAPTURE)) {
            $this->currentMentionStartPosition = $matches[0][1];
            $this->mentionQuery = $matches[1][0]; // Triggers updatedMentionQuery
            // Hide hashtag suggestions if we're clearly typing a mention
            $this->hashtagQuery = '';
            $this->hashtagSuggestions = [];
            return;
        } else {
            // If no mention pattern is at the end, reset mention query
            if (!empty($this->mentionQuery)) {
                $this->mentionQuery = '';
                $this->mentionSuggestions = [];
            }
        }
    }

    public function updatedHashtagQuery($query)
    {
        if (strlen($query) >= 1) { // Minimum 1 char after #
            try {
                // Assuming the API is on the same domain, session cookies should handle auth for Sanctum
                $response = Http::get(url('/api/hashtags/search'), [
                    'query' => $query,
                    'limit' => 7
                ]);

                if ($response->successful()) {
                    $this->hashtagSuggestions = $response->json();
                    $this->showHashtagSuggestions = !empty($this->hashtagSuggestions);
                } else {
                    Log::error('Hashtag API search failed: ' . $response->status(), $response->json());
                    $this->hashtagSuggestions = [];
                    $this->showHashtagSuggestions = false;
                }
            } catch (\Exception $e) {
                Log::error('Error calling hashtag API: ' . $e->getMessage());
                $this->hashtagSuggestions = [];
                $this->showHashtagSuggestions = false;
            }
        } else {
            $this->hashtagSuggestions = [];
            $this->showHashtagSuggestions = false;
        }
    }

    public function selectHashtag($selectedHashtagName)
    {
        if ($this->currentHashtagStartPosition !== null) {
            $textBeforeHashtag = substr($this->content, 0, $this->currentHashtagStartPosition);
            
            // Determine the end of the typed hashtag (e.g., "#que" or "#query")
            $lengthOfTypedHashtag = strlen($this->hashtagQuery); // This is just the query part, e.g., "que"

            // The part of the content string to be replaced starts at currentHashtagStartPosition
            // and its length is 1 (for '#') + lengthOfTypedHashtag
            $originalHashtagLength = 1 + $lengthOfTypedHashtag;

            $textAfterOriginalHashtag = substr($this->content, $this->currentHashtagStartPosition + $originalHashtagLength);

            $this->content = $textBeforeHashtag . '#' . $selectedHashtagName . ' ' . $textAfterOriginalHashtag;
        }

        // Reset hashtag state
        $this->hashtagQuery = '';
        $this->hashtagSuggestions = [];
        $this->showHashtagSuggestions = false;
        $this->currentHashtagStartPosition = null;
        $this->dispatch('contentUpdated', $this->content);
    }
    
    public function closeHashtagSuggestions()
    {
        $this->showHashtagSuggestions = false;
    }

    // Mention Autocomplete Logic
    public function updatedMentionQuery($query)
    {
        if (strlen($query) >= 1) {
            try {
                $response = Http::get(url('/api/users/search'), [
                    'query' => $query,
                    'limit' => 7
                ]);

                if ($response->successful()) {
                    $this->mentionSuggestions = $response->json();
                    $this->showMentionSuggestions = !empty($this->mentionSuggestions);
                } else {
                    Log::error('User API search failed: ' . $response->status(), $response->json());
                    $this->mentionSuggestions = [];
                    $this->showMentionSuggestions = false;
                }
            } catch (\Exception $e) {
                Log::error('Error calling User API: ' . $e->getMessage());
                $this->mentionSuggestions = [];
                $this->showMentionSuggestions = false;
            }
        } else {
            $this->mentionSuggestions = [];
            $this->showMentionSuggestions = false;
        }
    }

    public function selectMention($selectedUsername)
    {
        if ($this->currentMentionStartPosition !== null) {
            $textBeforeMention = substr($this->content, 0, $this->currentMentionStartPosition);
            $lengthOfTypedMention = strlen($this->mentionQuery);
            $originalMentionLength = 1 + $lengthOfTypedMention; // for '@' + query
            $textAfterOriginalMention = substr($this->content, $this->currentMentionStartPosition + $originalMentionLength);

            $this->content = $textBeforeMention . '@' . $selectedUsername . ' ' . $textAfterOriginalMention;
        }

        $this->mentionQuery = '';
        $this->mentionSuggestions = [];
        $this->showMentionSuggestions = false;
        $this->currentMentionStartPosition = null;
        $this->dispatch('contentUpdated', $this->content);
    }

    public function closeMentionSuggestions()
    {
        $this->showMentionSuggestions = false;
    }
}
