<div class="bg-white dark:bg-zinc-800 rounded-lg shadow-md p-6">
    <div class="flex items-center justify-between mb-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
            <flux:icon.share class="w-5 h-5 mr-2 text-indigo-500" />
            Visualização da Rede
        </h3>
        
        @if(Auth::check() && Auth::id() !== $user->id)
            <div class="text-sm text-gray-600 dark:text-gray-400">
                Conexões em comum com {{ $user->name }}
            </div>
        @endif
    </div>

    @if(!empty($networkData))
        {{-- Área de Visualização da Rede --}}
        <div class="relative bg-gray-50 dark:bg-zinc-700 rounded-lg p-8 min-h-96 overflow-hidden">
            {{-- Usuário Central --}}
            <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-10">
                <div class="relative group">
                    <div class="w-20 h-20 rounded-full border-4 {{ $this->getConnectionColor($networkData['center']['type']) }} bg-white dark:bg-zinc-800 overflow-hidden shadow-lg">
                        <img 
                            src="{{ $networkData['center']['avatar'] }}" 
                            alt="{{ $networkData['center']['name'] }}"
                            class="w-full h-full object-cover"
                        >
                    </div>
                    <div class="absolute -bottom-8 left-1/2 transform -translate-x-1/2 text-center">
                        <div class="text-sm font-medium text-gray-900 dark:text-white">
                            {{ $networkData['center']['name'] }}
                        </div>
                        <div class="text-xs text-gray-600 dark:text-gray-400">
                            {{ $this->getConnectionLabel($networkData['center']['type']) }}
                        </div>
                    </div>
                </div>
            </div>

            {{-- Usuário Atual (se visualizando outro perfil) --}}
            @if(isset($networkData['current_user']))
                <div class="absolute top-4 left-4">
                    <div class="relative group">
                        <div class="w-16 h-16 rounded-full border-4 {{ $this->getConnectionColor($networkData['current_user']['type']) }} bg-white dark:bg-zinc-800 overflow-hidden shadow-lg">
                            <img 
                                src="{{ $networkData['current_user']['avatar'] }}" 
                                alt="{{ $networkData['current_user']['name'] }}"
                                class="w-full h-full object-cover"
                            >
                        </div>
                        <div class="absolute -bottom-6 left-1/2 transform -translate-x-1/2 text-center">
                            <div class="text-xs font-medium text-gray-900 dark:text-white">
                                Você
                            </div>
                        </div>
                    </div>
                </div>
            @endif

            {{-- Conexões em Círculo --}}
            @if(!empty($networkData['connections']))
                @foreach($networkData['connections'] as $index => $connection)
                    @php
                        $angle = ($index / count($networkData['connections'])) * 360;
                        $radius = 140;
                        $x = 50 + ($radius * cos(deg2rad($angle))) / 3.84; // Ajuste para porcentagem
                        $y = 50 + ($radius * sin(deg2rad($angle))) / 3.84; // Ajuste para porcentagem
                    @endphp
                    
                    <div 
                        class="absolute transform -translate-x-1/2 -translate-y-1/2 cursor-pointer group"
                        style="left: {{ $x }}%; top: {{ $y }}%;"
                        wire:click="showConnectionDetails({{ $connection['id'] }})"
                    >
                        {{-- Linha de Conexão --}}
                        <div class="absolute top-1/2 left-1/2 w-20 h-0.5 bg-gray-300 dark:bg-gray-600 origin-left transform -translate-y-1/2 rotate-{{ $angle }}deg opacity-30"></div>
                        
                        {{-- Avatar da Conexão --}}
                        <div class="relative">
                            <div class="w-12 h-12 rounded-full border-2 {{ $this->getConnectionColor($connection['type']) }} bg-white dark:bg-zinc-800 overflow-hidden shadow-md group-hover:shadow-lg transition-shadow">
                                <img 
                                    src="{{ $connection['avatar'] }}" 
                                    alt="{{ $connection['name'] }}"
                                    class="w-full h-full object-cover"
                                >
                            </div>
                            
                            {{-- Indicador de Força da Conexão --}}
                            <div class="absolute -bottom-1 -right-1 w-4 h-4 rounded-full border-2 border-white dark:border-zinc-800 
                                {{ $connection['connection_strength'] >= 70 ? 'bg-green-500' : 
                                   ($connection['connection_strength'] >= 40 ? 'bg-yellow-500' : 'bg-gray-400') }}">
                            </div>
                            
                            {{-- Tooltip --}}
                            <div class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-black text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-20">
                                {{ $connection['name'] }}
                                <div class="text-xs text-gray-300">{{ $this->getConnectionLabel($connection['type']) }}</div>
                            </div>
                        </div>
                    </div>
                @endforeach
            @endif
        </div>

        {{-- Estatísticas --}}
        <div class="mt-6 grid grid-cols-2 md:grid-cols-4 gap-4">
            @if(isset($networkData['stats']['followers_count']))
                <div class="text-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                    <div class="text-xl font-bold text-blue-600 dark:text-blue-400">
                        {{ $networkData['stats']['followers_count'] }}
                    </div>
                    <div class="text-sm text-blue-700 dark:text-blue-300">Seguidores</div>
                </div>
                
                <div class="text-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                    <div class="text-xl font-bold text-green-600 dark:text-green-400">
                        {{ $networkData['stats']['following_count'] }}
                    </div>
                    <div class="text-sm text-green-700 dark:text-green-300">Seguindo</div>
                </div>
            @endif

            @if(isset($networkData['stats']['mutual_followers']))
                <div class="text-center p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                    <div class="text-xl font-bold text-purple-600 dark:text-purple-400">
                        {{ $networkData['stats']['mutual_followers'] }}
                    </div>
                    <div class="text-sm text-purple-700 dark:text-purple-300">Seguidores Mútuos</div>
                </div>
                
                <div class="text-center p-3 bg-indigo-50 dark:bg-indigo-900/20 rounded-lg">
                    <div class="text-xl font-bold text-indigo-600 dark:text-indigo-400">
                        {{ $networkData['stats']['total_mutual'] }}
                    </div>
                    <div class="text-sm text-indigo-700 dark:text-indigo-300">Total em Comum</div>
                </div>
            @endif
        </div>

        {{-- Legenda --}}
        <div class="mt-4 flex flex-wrap gap-4 text-sm">
            <div class="flex items-center">
                <div class="w-3 h-3 rounded-full bg-green-500 mr-2"></div>
                <span class="text-gray-600 dark:text-gray-400">Conexão Forte</span>
            </div>
            <div class="flex items-center">
                <div class="w-3 h-3 rounded-full bg-yellow-500 mr-2"></div>
                <span class="text-gray-600 dark:text-gray-400">Conexão Média</span>
            </div>
            <div class="flex items-center">
                <div class="w-3 h-3 rounded-full bg-gray-400 mr-2"></div>
                <span class="text-gray-600 dark:text-gray-400">Conexão Fraca</span>
            </div>
        </div>
    @else
        <div class="text-center py-8">
            <flux:icon.users class="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h4 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
                Nenhuma conexão encontrada
            </h4>
            <p class="text-gray-600 dark:text-gray-400">
                Este usuário ainda não possui conexões para visualizar.
            </p>
        </div>
    @endif

    {{-- Modal de Detalhes da Conexão --}}
    @if($showModal && $selectedConnection)
        <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" wire:click="closeModal">
            <div class="bg-white dark:bg-zinc-800 rounded-lg p-6 max-w-md w-full mx-4" wire:click.stop>
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                        Detalhes da Conexão
                    </h3>
                    <flux:button wire:click="closeModal" variant="ghost" size="sm">
                        <flux:icon.x-mark class="w-5 h-5" />
                    </flux:button>
                </div>
                
                <div class="flex items-center mb-4">
                    <img 
                        src="{{ $selectedConnection['avatar'] }}" 
                        alt="{{ $selectedConnection['name'] }}"
                        class="w-16 h-16 rounded-full border-2 {{ $this->getConnectionColor($selectedConnection['type']) }} mr-4"
                    >
                    <div>
                        <h4 class="font-medium text-gray-900 dark:text-white">
                            {{ $selectedConnection['name'] }}
                        </h4>
                        <p class="text-sm text-gray-600 dark:text-gray-400">
                            {{ '@' . $selectedConnection['username'] }}
                        </p>
                        <p class="text-sm text-purple-600 dark:text-purple-400">
                            {{ $this->getConnectionLabel($selectedConnection['type']) }}
                        </p>
                    </div>
                </div>
                
                <div class="mb-4">
                    <div class="flex items-center justify-between mb-2">
                        <span class="text-sm text-gray-600 dark:text-gray-400">Força da Conexão</span>
                        <span class="text-sm font-medium text-gray-900 dark:text-white">
                            {{ $selectedConnection['connection_strength'] }}%
                        </span>
                    </div>
                    <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                        <div 
                            class="h-2 rounded-full {{ $selectedConnection['connection_strength'] >= 70 ? 'bg-green-500' : 
                                ($selectedConnection['connection_strength'] >= 40 ? 'bg-yellow-500' : 'bg-gray-400') }}"
                            style="width: {{ $selectedConnection['connection_strength'] }}%"
                        ></div>
                    </div>
                </div>
                
                <div class="flex gap-2">
                    <flux:button 
                        href="/{{ $selectedConnection['username'] }}" 
                        wire:navigate
                        variant="primary" 
                        size="sm"
                        class="flex-1"
                    >
                        <flux:icon.eye class="w-4 h-4 mr-1" />
                        Ver Perfil
                    </flux:button>
                    
                    <flux:button 
                        href="{{ route('caixa_de_mensagens', ['user' => $selectedConnection['username']]) }}"
                        wire:navigate
                        variant="ghost" 
                        size="sm"
                        class="flex-1"
                    >
                        <flux:icon.chat-bubble-left class="w-4 h-4 mr-1" />
                        Mensagem
                    </flux:button>
                </div>
            </div>
        </div>
    @endif
</div>
