<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" class="{{ session('appearance', 'dark') }}">
    <head>
        @include('partials.head')
        <!-- CSRF Token para requisições AJAX -->
        <meta name="csrf-token" content="{{ csrf_token() }}">
    </head>
    <!-- Google tag (gtag.js) -->
        <script async src="https://www.googletagmanager.com/gtag/js?id=G-B0FE0C9J1S"></script>
        <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', 'G-B0FE0C9J1S');
        </script>


    <body class="min-h-screen bg-white dark:bg-zinc-800">
        <!-- Visitor Timer (for visitante role users) -->
        @livewire('visitor-timer')

        <!-- Notification Toast -->
        @livewire('toast-notification')

        <!-- Message Notifier (checks for new messages on all pages) -->
        @livewire('message-notifier')

        @php
            use Illuminate\Support\Facades\Storage;
            // Buscar a foto do usuário onde is_current é true
            $currentPhoto = auth()->user()->userPhotos()->where('is_current', true)->first();
            // Se existir uma foto atual, usar o path dela, caso contrário, usar o avatar padrão
            $avatarUrl = $currentPhoto ? Storage::url($currentPhoto->photo_path) : asset('images/users/avatar.svg');
        @endphp



        <flux:sidebar sticky stashable class="border-r border-zinc-200 bg-zinc-50 dark:border-zinc-700 dark:bg-zinc-900">
            <flux:sidebar.toggle class="lg:hidden" icon="x-mark" />

            <a href="{{ route('dashboard') }}" class="mr-5 flex items-center space-x-2" wire:navigate>
                <x-app-logo />
            </a>

            <flux:navlist variant="outline">
                <flux:navlist.group class="grid">
                    <flux:navlist.item icon="home" :href="route('dashboard')" :current="request()->routeIs('dashboard')" wire:navigate>
                        {{ __('Principal') }}
                    </flux:navlist.item>

                    <flux:navlist.item icon="magnifying-glass-circle" badge="{{ \App\Models\User::count() }}" :href="route('busca')" :current="request()->routeIs('busca')" wire:navigate>
                        {{ __('Busca') }}
                    </flux:navlist.item>

                    @php
                        $matchesCount = \App\Models\UserMatch::where('user_id', auth()->id())
                            ->where('is_matched', true)
                            ->count();
                    @endphp
                    <flux:navlist.item icon="map-pin" :badge="$matchesCount > 0 ? $matchesCount : null" :href="route('radar')" :current="request()->routeIs('radar')" wire:navigate>
                        {{ __('Radar') }}
                    </flux:navlist.item>

                    <flux:navlist.item icon="inbox" :badge="auth()->user()->unreadMessagesCount() > 0 ? auth()->user()->unreadMessagesCount() : null" :href="route('caixa_de_mensagens')" :current="request()->routeIs('caixa_de_mensagens')" wire:navigate>
                        {{ __('Mensagens') }}
                    </flux:navlist.item>

                    <flux:navlist.item icon="sparkles" :href="route('sugestoes')" :current="request()->routeIs('sugestoes')" wire:navigate>
                        {{ __('Sugestões') }}
                    </flux:navlist.item>

                    <flux:navlist.item icon="chart-bar" :href="route('ranking')" :current="request()->routeIs('ranking')" wire:navigate>
                        {{ __('Ranking') }}
                    </flux:navlist.item>

                    <flux:navlist.item icon="calendar-days" badge="{{ \App\Models\Event::active()->count() }}" :href="route('events.index')" :current="request()->routeIs('events.*')" wire:navigate>
                        {{ __('Eventos') }}
                    </flux:navlist.item>

                    <flux:navlist.item icon="user-group" badge="{{ \App\Models\Group::count() }}" :href="route('grupos.index')" :current="request()->routeIs('grupos.*')" wire:navigate>
                        {{ __('Grupos') }}
                    </flux:navlist.item>

                    <flux:navlist.item icon="book-open" badge="{{ \App\Models\Conto::count() }}" :href="route('contos')" :current="request()->routeIs('contos')" wire:navigate>
                        {{ __('Contos') }}
                    </flux:navlist.item>

                    <flux:navlist.item icon="puzzle-piece" :href="route('night-board.index')" :current="request()->routeIs('night-board.*')" wire:navigate>
                        {{ __('Monte Sua Noite') }}
                    </flux:navlist.item>

                    <flux:navlist.group expandable heading="Feed" class="lg:grid" :expanded="request()->routeIs('feed.*')">
                        <flux:navlist.item icon="photo" badge="{{ \App\Models\Post::whereNotNull('image')->count() }}" :href="route('feed_imagens')">Imagens</flux:navlist.item>
                        <flux:navlist.item icon="video-camera" badge="{{ \App\Models\Post::whereNotNull('video')->count() }}" :href="route('feed_videos')"> Vídeos</flux:navlist.item>
                    </flux:navlist.group>

                    <flux:navlist.group expandable heading="Loja" class="lg:grid" :expanded="request()->routeIs('shop.*')">
                        <flux:navlist.item icon="shopping-bag" :href="route('shop.index')" :current="request()->routeIs('shop.index')">
                            {{ __('Produtos') }}
                        </flux:navlist.item>
                        <flux:navlist.item icon="shopping-cart" :href="route('shop.cart')" :current="request()->routeIs('shop.cart')">
                            {{ __('Carrinho') }}
                        </flux:navlist.item>
                        <flux:navlist.item icon="clipboard-document-list" :href="route('shop.user.orders')" :current="request()->routeIs('shop.user.orders')">
                            {{ __('Meus Pedidos') }}
                        </flux:navlist.item>
                        <flux:navlist.item icon="heart" :href="route('shop.wishlist')" :current="request()->routeIs('shop.wishlist')">
                            {{ __('Lista de Desejos') }}
                        </flux:navlist.item>
                        <flux:navlist.item icon="arrow-down-tray" :href="route('shop.downloads')" :current="request()->routeIs('shop.downloads')">
                            {{ __('Meus Downloads') }}
                        </flux:navlist.item>
                    </flux:navlist.group>

                    <flux:navlist.item icon="wallet" :href="route('wallet.index')" :current="request()->routeIs('wallet.*')" wire:navigate>
                        <div class="flex items-center justify-between w-full">
                            <span>{{ __('Carteira') }}</span>
                            <span class="text-sm font-medium text-green-600 dark:text-green-400">R$ {{ number_format(auth()->user()->wallet->balance, 2, ',', '.') }}</span>
                        </div>
                    </flux:navlist.item>

                </flux:navlist.group>
            </flux:navlist>
            <flux:spacer />

            <!-- Desktop User Menu -->
            <flux:dropdown position="bottom" align="start">
                <flux:profile :name="auth()->user()->name"
                    :avatar="$avatarUrl"
                    icon-trailing="chevrons-up-down"
                />

                <flux:menu class="w-[220px]">
                    <flux:menu.radio.group>
                        <div class="p-0 text-sm font-normal">
                            <div class="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
                                <span class="relative flex h-8 w-8 shrink-0 overflow-hidden rounded-lg">
                                    <img src="{{ $avatarUrl }}"
                                         
                                         alt="{{ auth()->user()->name }}" />
                                </span>

                                <div class="grid flex-1 text-left text-sm leading-tight">
                                    <span class="truncate font-semibold">{{ auth()->user()->name }}</span>
                                    <span class="truncate text-xs">{{ auth()->user()->email }}</span>
                                </div>
                            </div>
                        </div>
                    </flux:menu.radio.group>

                    <flux:menu.separator />

                    <flux:menu.radio.group>
                        <flux:menu.item :href="route('settings.profile')" icon="cog" wire:navigate >{{ __('Configurações') }}</flux:menu.item>

                        @if(auth()->user()->role === 'admin')
                            <flux:menu.separator />
                            <flux:menu.item icon="chart-bar" :href="route('admin.dashboard')" wire:navigate>Dashboard Admin</flux:menu.item>
                            <flux:menu.item icon="cube" :href="route('admin.products')" wire:navigate>Produtos</flux:menu.item>
                            <flux:menu.item icon="folder" :href="route('admin.categories')" wire:navigate>Categorias</flux:menu.item>
                            <flux:menu.item icon="clipboard-document-list" :href="route('admin.orders')" wire:navigate>Pedidos</flux:menu.item>
                            <flux:menu.item icon="ticket" :href="route('admin.coupons')" wire:navigate>Cupons</flux:menu.item>
                            <flux:menu.item icon="users" :href="route('admin.users')" wire:navigate>Usuários</flux:menu.item>
                            <flux:menu.item icon="chat-bubble-left-right" :href="route('admin.support.tickets')" wire:navigate>Suporte</flux:menu.item>
                            <flux:menu.item icon="wallet" :href="route('admin.wallets')" wire:navigate>Carteiras</flux:menu.item>
                            <flux:menu.item icon="rectangle-group" :href="route('admin.levels')" wire:navigate>Níveis</flux:menu.item>
                            <flux:menu.item icon="star" :href="route('admin.achievements')" wire:navigate>Conquistas</flux:menu.item>
                        @endif
                    </flux:menu.radio.group>

                    <flux:menu.separator />

                    <form method="POST" action="{{ route('logout') }}" class="w-full">
                        @csrf
                        <flux:menu.item as="button" type="submit" icon="arrow-right-start-on-rectangle" class="w-full">
                            {{ __('Sair') }}
                        </flux:menu.item>
                    </form>
                </flux:menu>
            </flux:dropdown>
        </flux:sidebar>

        <!-- Header principal (oculto em mobile) -->
        <flux:header container class="bg-zinc-50 dark:bg-zinc-900 border-b border-zinc-200 dark:border-zinc-700 max-lg:hidden">
            <flux:navbar class="-mb-px">
                <flux:navbar.item icon="map-pin" :href="route('radar')" >Radar</flux:navbar.item>
                <flux:navbar.item icon="inbox" badge="{{ auth()->user()->unreadMessagesCount() }}" :href="route('caixa_de_mensagens')">Mensagens</flux:navbar.item>

                @livewire('follow-request-notifications')
                <livewire:notifications />
            </flux:navbar>
            <flux:spacer />
            <flux:navbar class="me-4">
                <flux:navbar.item icon="currency-dollar" :href="route('wallet.index')" wire:navigate>
                    <div class="flex items-center gap-1.5">
                        <span class="text-sm font-medium text-green-400 dark:text-green-400">R$ {{ number_format(auth()->user()->wallet->balance, 2, ',', '.') }}</span>
                    </div>
                </flux:navbar.item>
                <!-- Mini Cart -->
                @livewire('shop.mini-cart')

                <flux:navbar.item icon="magnifying-glass" href="#" label="Buscar" x-on:click.prevent="$dispatch('open-search-modal')" />
                <flux:navbar.item icon="cog-6-tooth" :href="route('settings.profile')" label="Settings" />

                <!-- Help Dropdown -->
                <flux:dropdown>
                    <flux:navbar.item icon="question-mark-circle" label="Ajuda" />
                    <flux:menu class="w-56">
                        <flux:menu.item icon="book-open" :href="route('support.index', ['activeTab' => 'tutorials'])" wire:navigate>
                            Tutoriais
                        </flux:menu.item>

                        <flux:menu.item icon="question-mark-circle" :href="route('support.index', ['activeTab' => 'faq'])" wire:navigate>
                            FAQ
                        </flux:menu.item>

                        <flux:menu.item icon="ticket" :href="route('support.index', ['activeTab' => 'tickets'])" wire:navigate>
                            Meus Tickets
                        </flux:menu.item>

                        <flux:menu.separator />

                        <flux:menu.item icon="chat-bubble-left-right" :href="route('support.index')" wire:navigate>
                            Central de Ajuda
                        </flux:menu.item>
                    </flux:menu>
                </flux:dropdown>
            </flux:navbar>
            <flux:dropdown position="top" align="start">
                <flux:profile :avatar="$avatarUrl" />
                <flux:menu>
                    <flux:menu.radio.group>
                        <flux:menu.radio checked>{{ auth()->user()->name }}</flux:menu.radio>
                    </flux:menu.radio.group>

                    <flux:menu.separator />
                    <flux:menu.item icon="user" :href="'/' . auth()->user()->username">Meu Perfil</flux:menu.item>
                    <flux:menu.item icon="trophy" :href="route('points.history')" wire:navigate>Pontuação</flux:menu.item>
                    <flux:menu.item icon="user-plus" :href="route('follow.requests')" wire:navigate>
                        Solicitações para seguir
                        @php
                            $pendingCount = \App\Models\FollowRequest::where('receiver_id', auth()->id())
                                ->where('status', 'pending')
                                ->count();
                        @endphp
                        @if($pendingCount > 0)
                            <span class="ml-2 px-2 py-0.5 text-xs bg-red-500 text-white rounded-full">
                                {{ $pendingCount }}
                            </span>
                        @endif
                    </flux:menu.item>
                    <flux:menu.item icon="eye" :href="route('profile.visitors')" wire:navigate>Meus Visitantes</flux:menu.item>
                    <flux:menu.item icon="cog" :href="route('settings.profile')">Configurações</flux:menu.item>
                    <flux:menu.item icon="arrow-right-start-on-rectangle" :href="route('renovar-vip')">Assinatura VIP</flux:menu.item>
                    <flux:menu.item icon="wallet" :href="route('wallet.index')">
                        <div class="flex items-center justify-between w-full">
                            <span>Minha Carteira</span>
                            <span class="text-sm font-medium text-indigo-600 dark:text-indigo-400">R$ {{ number_format(auth()->user()->wallet->balance, 2, ',', '.') }}</span>
                        </div>
                    </flux:menu.item>
                    <flux:menu.item icon="credit-card" :href="route('meus-pagamentos')">Meus Pagamentos</flux:menu.item>
                    <form method="POST" action="{{ route('logout') }}" class="w-full">
                        @csrf
                        <flux:menu.item as="button" type="submit" icon="arrow-right-start-on-rectangle" class="w-full">
                            {{ __('Sair') }}
                        </flux:menu.item>
                    </form>
                </flux:menu>
            </flux:dropdown>
        </flux:header>

        <!-- Mobile User Menu (visível apenas em mobile) -->
        <flux:header class="lg:hidden bg-zinc-50 dark:bg-zinc-900 border-b border-zinc-200 dark:border-zinc-700">
            <flux:sidebar.toggle class="lg:hidden" icon="bars-2" inset="left" />

            <flux:spacer />

            <!-- Mobile Search Button -->
            <button
                class="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 focus:outline-none"
                x-on:click.prevent="$dispatch('open-search-modal')"
            >
                <x-flux::icon name="magnifying-glass" class="w-5 h-5" />
            </button>

            <!-- Mobile Wallet Balance -->
            <flux:navbar.item icon="currency-dollar" :href="route('wallet.index')" wire:navigate class="mr-2">
                <div class="flex items-center gap-1.5">
                    <span class="text-sm font-medium text-green-400 dark:text-green-400">R$ {{ number_format(auth()->user()->wallet->balance, 2, ',', '.') }}</span>
                </div>
            </flux:navbar.item>

            <!-- Mobile Mini Cart -->
            @livewire('shop.mini-cart')

            <flux:dropdown position="top" align="start">
                <flux:profile
                    :initials="auth()->user()->initials()"
                    :avatar="$avatarUrl"
                    icon-trailing="chevron-down"
                />

                <flux:menu class="w-[280px]">
                    <flux:menu.radio.group>
                        <flux:menu.radio checked>{{ auth()->user()->name }}</flux:menu.radio>
                    </flux:menu.radio.group>

                    <flux:menu.separator />
                    <flux:menu.item icon="user" :href="'/' . auth()->user()->username" wire:navigate>Meu Perfil</flux:menu.item>
                    <flux:menu.item icon="trophy" :href="route('points.history')" wire:navigate>Pontuação</flux:menu.item>
                    <flux:menu.item icon="user-plus" :href="route('follow.requests')" wire:navigate>
                        Solicitações para seguir
                        @php
                            $pendingCount = \App\Models\FollowRequest::where('receiver_id', auth()->id())
                                ->where('status', 'pending')
                                ->count();
                        @endphp
                        @if($pendingCount > 0)
                            <span class="ml-2 px-2 py-0.5 text-xs bg-red-500 text-white rounded-full">
                                {{ $pendingCount }}
                            </span>
                        @endif
                    </flux:menu.item>
                    <flux:menu.item icon="eye" :href="route('profile.visitors')" wire:navigate>Meus Visitantes</flux:menu.item>
                    <flux:menu.item icon="cog" :href="route('settings.profile')" wire:navigate>Configurações</flux:menu.item>

                    @if(auth()->user()->role === 'admin')
                        <flux:menu.separator />
                        <flux:menu.item icon="chart-bar" :href="route('admin.dashboard')" wire:navigate>Dashboard Admin</flux:menu.item>
                        <flux:menu.item icon="cube" :href="route('admin.products')" wire:navigate>Produtos</flux:menu.item>
                        <flux:menu.item icon="folder" :href="route('admin.categories')" wire:navigate>Categorias</flux:menu.item>
                        <flux:menu.item icon="clipboard-document-list" :href="route('admin.orders')" wire:navigate>Pedidos</flux:menu.item>
                        <flux:menu.item icon="ticket" :href="route('admin.coupons')" wire:navigate>Cupons</flux:menu.item>
                        <flux:menu.item icon="users" :href="route('admin.users')" wire:navigate>Usuários</flux:menu.item>
                        <flux:menu.item icon="chat-bubble-left-right" :href="route('admin.support.tickets')" wire:navigate>Suporte</flux:menu.item>
                        <flux:menu.item icon="wallet" :href="route('admin.wallets')" wire:navigate>Carteiras</flux:menu.item>
                        <flux:menu.item icon="rectangle-group" :href="route('admin.levels')" wire:navigate>Níveis</flux:menu.item>
                        <flux:menu.item icon="star" :href="route('admin.achievements')" wire:navigate>Conquistas</flux:menu.item>
                    @endif
                    <flux:menu.item icon="star" :href="route('renovar-vip')" wire:navigate>Renovar VIP</flux:menu.item>
                    <flux:menu.item icon="wallet" :href="route('wallet.index')" wire:navigate>
                        <div class="flex items-center justify-between w-full">
                            <span>Minha Carteira</span>
                            <span class="text-sm font-medium text-indigo-600 dark:text-indigo-400">R$ {{ number_format(auth()->user()->wallet->balance, 2, ',', '.') }}</span>
                        </div>
                    </flux:menu.item>
                    <flux:menu.item icon="credit-card" :href="route('meus-pagamentos')" wire:navigate>Meus Pagamentos</flux:menu.item>

                    <flux:menu.separator />

                    <form method="POST" action="{{ route('logout') }}" class="w-full">
                        @csrf
                        <flux:menu.item as="button" type="submit" icon="arrow-right-start-on-rectangle" class="w-full">
                            {{ __('Sair') }}
                        </flux:menu.item>
                    </form>
                </flux:menu>
            </flux:dropdown>
        </flux:header>

        {{ $slot }}

        @fluxScripts

        

        <!-- Componente de notificação de status de amigos -->
        <livewire:friend-status-notifier />

        <!-- Componente de busca modal -->
        <livewire:search-modal />

        <!-- Scripts para correções e notificações -->
        <script src="{{ asset('js/livewire-fix.js') }}"></script>
        <script src="{{ asset('js/toast-fix.js') }}"></script>
        <script src="{{ asset('js/toast-tester.js') }}"></script>
        <script src="{{ asset('js/mention-hashtag-handler.js') }}" defer></script>
        <!-- Script para menções e hashtags -->
        <script>
            // Verificar se o script foi carregado
            console.log('Inicializando MentionHashtagHandler...');
        </script>
        

        <script>
            // Correção para o erro de showTooltip já está definida no tooltip-handler.js

            // Function to trigger confetti animation - optimized for performance
            window.triggerConfetti = function() {
                // Create a canvas element with all styles before adding to DOM
                const canvas = document.createElement('canvas');

                // Set all styles before appending to reduce reflows
                Object.assign(canvas.style, {
                    position: 'fixed',
                    top: '0',
                    left: '0',
                    width: '100%',
                    height: '100%',
                    pointerEvents: 'none',
                    zIndex: '9999'
                });

                // Get context and set dimensions once
                const ctx = canvas.getContext('2d');
                canvas.width = window.innerWidth;
                canvas.height = window.innerHeight;

                // Now append to DOM after all styles are set
                document.body.appendChild(canvas);

                // Use a smaller number of particles for better performance
                const confettiCount = 200;
                const confetti = [];

                // Pre-calculate random values to avoid doing it in the animation loop
                for (let i = 0; i < confettiCount; i++) {
                    confetti.push({
                        x: Math.random() * canvas.width,
                        y: Math.random() * canvas.height - canvas.height,
                        r: Math.random() * 4 + 2, // Slightly smaller particles
                        dx: Math.random() * 3 - 1.5,
                        dy: Math.random() * 3 + 1,
                        color: `hsl(${Math.random() * 360}, 100%, 50%)`
                    });
                }

                // Throttled resize handler
                let resizeTimeout;
                const handleResize = () => {
                    clearTimeout(resizeTimeout);
                    resizeTimeout = setTimeout(() => {
                        canvas.width = window.innerWidth;
                        canvas.height = window.innerHeight;
                    }, 100);
                };

                window.addEventListener('resize', handleResize);

                // Use requestAnimationFrame for smooth animation
                let animationId;
                function animate() {
                    ctx.clearRect(0, 0, canvas.width, canvas.height);

                    // Batch drawing operations
                    for (let i = 0; i < confetti.length; i++) {
                        const p = confetti[i];
                        ctx.beginPath();
                        ctx.arc(p.x, p.y, p.r, 0, Math.PI * 2);
                        ctx.fillStyle = p.color;
                        ctx.fill();

                        // Update position
                        p.x += p.dx;
                        p.y += p.dy;
                        if (p.y > canvas.height) p.y = -p.r;
                    }

                    animationId = requestAnimationFrame(animate);
                }

                // Start animation
                animationId = requestAnimationFrame(animate);

                // Clean up after animation
                setTimeout(() => {
                    cancelAnimationFrame(animationId);
                    window.removeEventListener('resize', handleResize);
                    canvas.remove();
                }, 2000);
            };

            // Function to trigger XP popup - optimized
            window.triggerXpPopup = function(points) {
                // Create popup with all styles before adding to DOM
                const popup = document.createElement('div');
                popup.textContent = `+${points} XP!`;

                // Apply all styles at once
                Object.assign(popup.style, {
                    position: 'fixed',
                    top: '50%',
                    left: '50%',
                    transform: 'translate(-50%, -50%)',
                    color: 'white',
                    fontSize: '2rem',
                    fontWeight: 'bold',
                    backgroundColor: '#3b82f6',
                    padding: '0.75rem 1.5rem',
                    borderRadius: '0.5rem',
                    boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
                    animation: 'pulse 2s infinite',
                    zIndex: '10000'
                });

                // Add keyframes for pulse animation if not already present
                if (!document.getElementById('xp-popup-style')) {
                    const style = document.createElement('style');
                    style.id = 'xp-popup-style';
                    style.textContent = `
                        @keyframes pulse {
                            0%, 100% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
                            50% { opacity: 0.8; transform: translate(-50%, -50%) scale(1.05); }
                        }
                    `;
                    document.head.appendChild(style);
                }

                // Append to DOM after all styles are set
                document.body.appendChild(popup);

                // Remove after animation
                setTimeout(() => popup.remove(), 2000);
            };

            // Listener para o evento reward-earned (usando a sintaxe do Livewire 3)
            document.addEventListener('livewire:initialized', () => {
                // No Livewire 3, usamos Livewire.on em vez de Livewire.addEventListener
                Livewire.on('reward-earned', (data) => {
                    // Use requestIdleCallback if available for non-critical UI updates
                    if (window.requestIdleCallback) {
                        requestIdleCallback(() => {
                            window.triggerConfetti();
                            window.triggerXpPopup(data.points);
                        });
                    } else {
                        // Fallback to setTimeout for browsers that don't support requestIdleCallback
                        setTimeout(() => {
                            window.triggerConfetti();
                            window.triggerXpPopup(data.points);
                        }, 0);
                    }
                });
            });
        </script>
    </body>
</html>
