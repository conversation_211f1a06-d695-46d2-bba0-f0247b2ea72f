<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\User;
use App\Models\AlbumMedia;
use App\Models\Album;
use Illuminate\Support\Facades\Auth;

class MediaGalleryFeatured extends Component
{
    public User $user;
    public $featuredMedias = [];
    public $recentAlbums = [];
    public $stats = [];
    public $selectedMedia = null;
    public $showLightbox = false;
    public $currentIndex = 0;

    public function mount(User $user)
    {
        $this->user = $user;
        $this->loadFeaturedContent();
    }

    public function loadFeaturedContent()
    {
        $this->loadStats();
        $this->loadFeaturedMedias();
        $this->loadRecentAlbums();
    }

    protected function loadStats()
    {
        $query = AlbumMedia::where('user_id', $this->user->id);
        
        // Se não é o próprio usuário, contar apenas mídias públicas
        if (!Auth::check() || Auth::id() !== $this->user->id) {
            $query->whereHas('album', function($q) {
                $q->where('privacy', 'public');
            });
        }

        $totalMedias = $query->count();
        $totalPhotos = $query->where('type', 'photo')->count();
        $totalVideos = $query->where('type', 'video')->count();
        
        $albumQuery = Album::where('user_id', $this->user->id);
        if (!Auth::check() || Auth::id() !== $this->user->id) {
            $albumQuery->where('privacy', 'public');
        }
        $totalAlbums = $albumQuery->count();

        $this->stats = [
            'total_medias' => $totalMedias,
            'total_photos' => $totalPhotos,
            'total_videos' => $totalVideos,
            'total_albums' => $totalAlbums,
        ];
    }

    protected function loadFeaturedMedias()
    {
        $query = AlbumMedia::where('user_id', $this->user->id)
            ->with('album');

        // Se não é o próprio usuário, mostrar apenas mídias de álbuns públicos
        if (!Auth::check() || Auth::id() !== $this->user->id) {
            $query->whereHas('album', function($q) {
                $q->where('privacy', 'public');
            });
        }

        $this->featuredMedias = $query->ordered()->take(12)->get();
    }

    protected function loadRecentAlbums()
    {
        $query = Album::where('user_id', $this->user->id)
            ->with(['medias' => function($q) {
                $q->take(3);
            }])
            ->withCount('medias');

        // Se não é o próprio usuário, mostrar apenas álbuns públicos
        if (!Auth::check() || Auth::id() !== $this->user->id) {
            $query->where('privacy', 'public');
        }

        $this->recentAlbums = $query->latest()->take(4)->get();
    }

    public function openLightbox($mediaId)
    {
        $this->selectedMedia = $this->featuredMedias->firstWhere('id', $mediaId);
        $this->currentIndex = $this->featuredMedias->search(function($media) use ($mediaId) {
            return $media->id == $mediaId;
        });
        $this->showLightbox = true;
    }

    public function closeLightbox()
    {
        $this->showLightbox = false;
        $this->selectedMedia = null;
        $this->currentIndex = 0;
    }

    public function nextMedia()
    {
        if ($this->currentIndex < $this->featuredMedias->count() - 1) {
            $this->currentIndex++;
            $this->selectedMedia = $this->featuredMedias[$this->currentIndex];
        }
    }

    public function previousMedia()
    {
        if ($this->currentIndex > 0) {
            $this->currentIndex--;
            $this->selectedMedia = $this->featuredMedias[$this->currentIndex];
        }
    }

    public function scrollToFullGallery()
    {
        $this->dispatch('scroll-to-element', element: '#media-gallery');
    }

    public function render()
    {
        return view('livewire.media-gallery-featured');
    }
}
