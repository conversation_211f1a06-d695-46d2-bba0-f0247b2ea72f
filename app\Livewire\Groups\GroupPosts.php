<?php

namespace App\Livewire\Groups;

use App\Models\Group;
use App\Models\Post;
use App\Models\UserPoint;
use App\Services\ContentProcessor;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Http; // Added for API calls
use Illuminate\Support\Facades\Log; // For logging errors
use Livewire\Component;
use Livewire\WithPagination;
use Livewire\WithFileUploads;

class GroupPosts extends Component
{
    use WithPagination, WithFileUploads;

    public $group;
    public $content = '';
    public $image;
    public $video;
    public $showPostForm = false;
    public $newComment = [];
    public $showComments = [];

    // Estados para edição de comentários
    public $editingComment = null;
    public $editCommentText = '';

    // Estados para exclusão de comentários
    public $showDeleteCommentModal = false;
    public $commentToDelete = null;

    // --- Autocomplete Properties ---
    // Context for which input field is active (e.g., 'new_comment_123' or 'edit_comment')
    public $activeAutocompleteContext = null;
    public $activeAutocompletePostId = null; // Stores postId for new comment context

    // Hashtag Autocomplete
    public $hashtagQuery = '';
    public $hashtagSuggestions = [];
    public $showHashtagSuggestions = false;
    protected $currentHashtagStartPosition = null;

    // Mention Autocomplete
    public $mentionQuery = '';
    public $mentionSuggestions = [];
    public $showMentionSuggestions = false;
    protected $currentMentionStartPosition = null;
    // --- End Autocomplete Properties ---

    protected $rules = [
        'content' => 'nullable|string|max:1000',
        'image' => 'nullable|image|max:2048',
        'video' => 'nullable|file|mimes:mp4,mov,avi|max:10240',
        'newComment.*' => 'required|string|min:1',
    ];

    protected $validationAttributes = [
        'content' => 'conteúdo',
        'image' => 'imagem',
        'video' => 'vídeo',
        'newComment.*' => 'comentário',
    ];

    protected function getListeners()
    {
        return [
            'toggleLike',
            'focusComment',
        ];
    }

    public function mount(Group $group)
    {
        $this->group = $group;

        // Check if the user can view this group
        if ($this->group->privacy === 'secret' && !Auth::check()) {
            abort(404);
        }

        if ($this->group->privacy === 'secret' && !Auth::user()->isMemberOf($this->group)) {
            abort(403, 'Você não tem permissão para visualizar este grupo.');
        }
    }

    public function togglePostForm()
    {
        $this->showPostForm = !$this->showPostForm;
    }

    public function createPost()
    {
        Log::info('Attempting to create a post in group: ' . $this->group->id);
        // Check if the user is a member of the group
        if (!Auth::user()->isMemberOf($this->group)) {
            session()->flash('error', 'Você precisa ser membro do grupo para criar postagens.');
            Log::warning('User not member of group: ' . Auth::id() . ' Group: ' . $this->group->id);
            return;
        }

        // Validate the post
        try {
            $this->validate([
                'content' => $this->image || $this->video ? 'nullable|string|max:1000' : 'required|string|min:3|max:1000',
                'image' => 'nullable|image|max:2048',
                'video' => 'nullable|file|mimes:mp4,mov,avi|max:10240',
            ]);
            Log::info('Post validation successful.', $this->validate()); // Log validated data
        } catch (\Illuminate\Validation\ValidationException $e) {
            Log::error('Post validation failed.', ['errors' => $e->errors()]);
            session()->flash('error', 'Erro de validação: Verifique o conteúdo, imagem ou vídeo.');
            return;
        }

        // Check if at least one field is filled
        if (empty($this->content) && !$this->image && !$this->video) {
            $this->addError('content', 'Você precisa adicionar conteúdo, uma imagem ou um vídeo.');
            Log::warning('Attempted to create empty post.');
            return;
        }

        $data = [
            'content' => $this->content,
            'user_id' => Auth::id(),
            'group_id' => $this->group->id,
        ];

        // Handle image upload
        if ($this->image) {
            try {
                $data['image'] = $this->image->store('posts/images', 'public');
                Log::info('Image uploaded successfully: ' . $data['image']);
            } catch (\Exception $e) {
                Log::error('Image upload failed: ' . $e->getMessage());
                session()->flash('error', 'Erro ao fazer upload da imagem.');
                return;
            }
        }

        // Handle video upload
        if ($this->video) {
            try {
                $data['video'] = $this->video->store('posts/videos', 'public');
                Log::info('Video uploaded successfully: ' . $data['video']);
            } catch (\Exception $e) {
                Log::error('Video upload failed: ' . $e->getMessage());
                session()->flash('error', 'Erro ao fazer upload do vídeo.');
                return;
            }
        }

        // Create the post
        try {
            $post = Post::create($data);
            Log::info('Post created successfully: ' . $post->id);
        } catch (\Exception $e) {
            Log::error('Post creation failed: ' . $e->getMessage());
            session()->flash('error', 'Erro ao criar a postagem.');
            return;
        }

        // Process hashtags for the new post
        if ($post && !empty($this->content)) {
            ContentProcessor::processHashtags($this->content, $post);
            ContentProcessor::processMentions($this->content, $post, Auth::id()); // Add this line
            Log::info('Hashtags and mentions processed for post: ' . $post->id);
        }

        // Reset the form
        $this->reset(['content', 'image', 'video']);
        $this->showPostForm = false;

        session()->flash('success', 'Postagem criada com sucesso!');
        Log::info('Post creation process finished for post: ' . $post->id);

        // Add points for creating a post in a group
        UserPoint::addPoints(
            Auth::id(),
            'group_post',
            15,
            "Criou uma postagem no grupo: {$this->group->name}",
            $post->id,
            Post::class
        );
    }

    public function deletePost(Post $post)
    {
        // Check if the user can delete this post
        if (Auth::id() !== $post->user_id && !Auth::user()->canManageGroup($this->group)) {
            session()->flash('error', 'Você não tem permissão para excluir esta postagem.');
            return;
        }

        // Delete the post
        $post->delete();

        session()->flash('success', 'Postagem excluída com sucesso!');
    }

    public function toggleLike($id)
    {
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        $postId = $id;
        $post = Post::findOrFail($postId);
        $user = Auth::user();

        if (!$user) return;

        $wasLiked = $post->isLikedBy($user);

        if ($wasLiked) {
            // Remove curtida
            $post->likedByUsers()->detach($user->id);

            // Remove notificação
            \App\Models\Notification::where([
                'sender_id' => $user->id,
                'post_id' => $post->id,
                'type' => 'like'
            ])->delete();

            // Remove pontos (apenas se o post não for do próprio usuário)
            if ($post->user_id !== $user->id) {
                UserPoint::removePoints(
                    $post->user_id,
                    'like',
                    5,
                    "Perdeu curtida de {$user->name}",
                    $post->id,
                    Post::class
                );
            }
        } else {
            // Adiciona curtida
            $post->likedByUsers()->attach($user->id);

            // Adiciona pontos ao usuário que curtiu (recompensa por engajamento)
            UserPoint::addPoints(
                $user->id,
                'like',
                2,
                "Curtiu postagem de " . ($post->user_id === $user->id ? "sua autoria" : $post->user->name),
                $post->id,
                Post::class
            );

            // Adiciona pontos ao autor do post (se não for o mesmo usuário)
            if ($post->user_id !== $user->id) {
                UserPoint::addPoints(
                    $post->user_id,
                    'like_received',
                    5,
                    "Recebeu curtida de {$user->name}",
                    $post->id,
                    Post::class
                );
            }

            // Dispara animação de recompensa
            $this->dispatch('reward-earned', points: 2);

            // Cria notificação se não for post próprio
            if ($post->user_id !== $user->id) {
                \App\Models\Notification::create([
                    'user_id' => $post->user_id,
                    'sender_id' => $user->id,
                    'type' => 'like',
                    'post_id' => $post->id
                ]);
            }
        }
    }

    public function focusComment($id)
    {
        $postId = $id;
        $this->showComments[$postId] = true;
    }

    public function addComment($postId)
    {
        $this->validate([
            "newComment.$postId" => 'required|min:1'
        ]);

        $post = Post::findOrFail($postId);
        $user = Auth::user();

        if (!$user) {
            return redirect()->route('login');
        }

        $comment = \App\Models\Comment::create([
            'user_id' => $user->id,
            'post_id' => $postId,
            'body' => $this->newComment[$postId]
        ]);

        // Processar hashtags para o comentário
        if ($comment && !empty($this->newComment[$postId])) {
            ContentProcessor::processHashtags($this->newComment[$postId], $comment);
            ContentProcessor::processMentions($this->newComment[$postId], $comment, Auth::id()); // Add this line
        }

        // Adiciona pontos ao usuário que comentou
        UserPoint::addPoints(
            $user->id,
            'comment',
            5,
            "Comentou na postagem de " . ($post->user_id === $user->id ? "sua autoria" : $post->user->name),
            $comment->id,
            \App\Models\Comment::class
        );

        // Adiciona pontos ao autor do post (se não for o mesmo usuário)
        if ($post->user_id !== $user->id) {
            UserPoint::addPoints(
                $post->user_id,
                'comment_received',
                3,
                "Recebeu comentário de {$user->name}",
                $comment->id,
                \App\Models\Comment::class
            );

            // Cria notificação
            \App\Models\Notification::create([
                'user_id' => $post->user_id,
                'sender_id' => $user->id,
                'type' => 'comment',
                'post_id' => $post->id,
                'comment_id' => $comment->id
            ]);
        }

        // Limpa o campo de comentário
        $this->newComment[$postId] = '';
    }

    // Métodos para edição de comentários
    public function startEditComment($commentId)
    {
        $comment = \App\Models\Comment::findOrFail($commentId);

        // Verificar se o usuário pode editar este comentário
        if (!$comment->canEdit(Auth::user())) {
            session()->flash('error', 'Você não tem permissão para editar este comentário.');
            return;
        }

        $this->editingComment = $commentId;
        $this->editCommentText = $comment->body;
    }

    public function cancelEditComment()
    {
        $this->editingComment = null;
        $this->editCommentText = '';
    }

    public function updateComment($commentId)
    {
        $this->validate([
            'editCommentText' => 'required|min:1|max:1000'
        ]);

        $comment = \App\Models\Comment::findOrFail($commentId);

        // Verificar se o usuário pode editar este comentário
        if (!$comment->canEdit(Auth::user())) {
            session()->flash('error', 'Você não tem permissão para editar este comentário.');
            return;
        }

        $oldContent = $comment->body; // Get old content for comparison if needed, though sync handles it
        $comment->body = $this->editCommentText;
        $comment->save(); // Save the raw, updated body

        // Process hashtags for the updated comment content
        // This will sync hashtags, adding new ones and removing old ones,
        // and update counts on the Hashtag model.
        ContentProcessor::processHashtags($this->editCommentText, $comment);
        ContentProcessor::processMentions($this->editCommentText, $comment, Auth::id()); // Add this line
        
        $comment->markAsEdited(); // This also calls save, ensure it's not redundant or combine logic if possible

        $this->editingComment = null;
        $this->editCommentText = '';

        session()->flash('success', 'Comentário editado com sucesso!');
    }

    // Métodos para exclusão de comentários
    public function openDeleteCommentModal($commentId)
    {
        $comment = \App\Models\Comment::findOrFail($commentId);

        // Verificar se o usuário pode deletar este comentário
        if (!$comment->canDelete(Auth::user())) {
            session()->flash('error', 'Você não tem permissão para excluir este comentário.');
            return;
        }

        $this->showDeleteCommentModal = true;
        $this->commentToDelete = $commentId;
    }

    public function closeDeleteCommentModal()
    {
        $this->showDeleteCommentModal = false;
        $this->commentToDelete = null;
    }

    public function deleteComment($commentId)
    {
        try {
            $comment = \App\Models\Comment::findOrFail($commentId);

            // Verificar se o usuário pode deletar este comentário
            if (!$comment->canDelete(Auth::user())) {
                session()->flash('error', 'Você não tem permissão para excluir este comentário.');
                return;
            }

            $comment->delete();

            $this->showDeleteCommentModal = false;
            $this->commentToDelete = null;

            session()->flash('success', 'Comentário excluído com sucesso!');
        } catch (\Exception $e) {
            session()->flash('error', 'Erro ao excluir comentário: ' . $e->getMessage());
        }
    }

    // --- Autocomplete Methods ---
    protected function determineActiveContent($context, $postId = null)
    {
        if ($context === 'edit_comment') {
            return $this->editCommentText;
        } elseif ($context === 'new_comment' && $postId && isset($this->newComment[$postId])) {
            return $this->newComment[$postId];
        }
        return '';
    }

    public function handleNewCommentInput($postId)
    {
        $this->activeAutocompleteContext = 'new_comment';
        $this->activeAutocompletePostId = $postId;
        $currentContent = $this->newComment[$postId] ?? '';
        $this->parseInputForAutocomplete($currentContent);
    }

    public function handleEditCommentInput()
    {
        $this->activeAutocompleteContext = 'edit_comment';
        $this->activeAutocompletePostId = null; // Not needed for edit comment
        $currentContent = $this->editCommentText;
        $this->parseInputForAutocomplete($currentContent);
    }
    
    protected function parseInputForAutocomplete($currentContent)
    {
        $this->showHashtagSuggestions = false;
        $this->showMentionSuggestions = false;

        // Check for Hashtag: #tag
        if (preg_match('/(?<=\s|^)\#([a-zA-Z0-9_]*)$/', $currentContent, $matches, PREG_OFFSET_CAPTURE)) {
            $this->currentHashtagStartPosition = $matches[0][1];
            $this->hashtagQuery = $matches[1][0];
            $this->mentionQuery = ''; $this->mentionSuggestions = []; // Reset other suggestions
            return;
        } else {
            if (!empty($this->hashtagQuery)) {
                $this->hashtagQuery = ''; $this->hashtagSuggestions = [];
            }
        }

        // Check for Mention: @user
        if (preg_match('/(?<=\s|^)\@([a-zA-Z0-9_.]*)$/', $currentContent, $matches, PREG_OFFSET_CAPTURE)) {
            $this->currentMentionStartPosition = $matches[0][1];
            $this->mentionQuery = $matches[1][0];
            $this->hashtagQuery = ''; $this->hashtagSuggestions = []; // Reset other suggestions
            return;
        } else {
             if (!empty($this->mentionQuery)) {
                $this->mentionQuery = ''; $this->mentionSuggestions = [];
            }
        }
    }

    public function updatedHashtagQuery($query)
    {
        if (empty($this->activeAutocompleteContext)) return; // Don't run if no context

        if (strlen($query) >= 1) {
            try {
                $response = Http::get(url('/api/hashtags/search'), ['query' => $query, 'limit' => 5]);
                if ($response->successful()) {
                    $this->hashtagSuggestions = $response->json();
                    $this->showHashtagSuggestions = !empty($this->hashtagSuggestions);
                } else {
                    $this->hashtagSuggestions = []; $this->showHashtagSuggestions = false;
                }
            } catch (\Exception $e) {
                Log::error('Error calling hashtag API in GroupPosts: ' . $e->getMessage());
                $this->hashtagSuggestions = []; $this->showHashtagSuggestions = false;
            }
        } else {
            $this->hashtagSuggestions = []; $this->showHashtagSuggestions = false;
        }
    }

    public function updatedMentionQuery($query)
    {
        if (empty($this->activeAutocompleteContext)) return; // Don't run if no context

        if (strlen($query) >= 1) {
            try {
                $response = Http::get(url('/api/users/search'), ['query' => $query, 'limit' => 5]);
                if ($response->successful()) {
                    $this->mentionSuggestions = $response->json();
                    $this->showMentionSuggestions = !empty($this->mentionSuggestions);
                } else {
                    $this->mentionSuggestions = []; $this->showMentionSuggestions = false;
                }
            } catch (\Exception $e) {
                Log::error('Error calling user API in GroupPosts: ' . $e->getMessage());
                $this->mentionSuggestions = []; $this->showMentionSuggestions = false;
            }
        } else {
            $this->mentionSuggestions = []; $this->showMentionSuggestions = false;
        }
    }

    public function selectHashtag($selectedHashtagName)
    {
        if ($this->currentHashtagStartPosition === null || empty($this->activeAutocompleteContext)) return;

        $contentToUpdate = '';
        if ($this->activeAutocompleteContext === 'edit_comment') {
            $contentToUpdate = &$this->editCommentText;
        } elseif ($this->activeAutocompleteContext === 'new_comment' && $this->activeAutocompletePostId) {
            $contentToUpdate = &$this->newComment[$this->activeAutocompletePostId];
        } else {
            return; // Should not happen
        }
        
        $textBeforeHashtag = substr($contentToUpdate, 0, $this->currentHashtagStartPosition);
        $originalHashtagLength = 1 + strlen($this->hashtagQuery);
        $textAfterOriginalHashtag = substr($contentToUpdate, $this->currentHashtagStartPosition + $originalHashtagLength);
        $contentToUpdate = $textBeforeHashtag . '#' . $selectedHashtagName . ' ' . $textAfterOriginalHashtag;

        $this->resetAutocompleteState();
    }

    public function selectMention($selectedUsername)
    {
        if ($this->currentMentionStartPosition === null || empty($this->activeAutocompleteContext)) return;

        $contentToUpdate = '';
         if ($this->activeAutocompleteContext === 'edit_comment') {
            $contentToUpdate = &$this->editCommentText;
        } elseif ($this->activeAutocompleteContext === 'new_comment' && $this->activeAutocompletePostId) {
            $contentToUpdate = &$this->newComment[$this->activeAutocompletePostId];
        } else {
            return; // Should not happen
        }

        $textBeforeMention = substr($contentToUpdate, 0, $this->currentMentionStartPosition);
        $originalMentionLength = 1 + strlen($this->mentionQuery);
        $textAfterOriginalMention = substr($contentToUpdate, $this->currentMentionStartPosition + $originalMentionLength);
        $contentToUpdate = $textBeforeMention . '@' . $selectedUsername . ' ' . $textAfterOriginalMention;
        
        $this->resetAutocompleteState();
    }
    
    protected function resetAutocompleteState()
    {
        $this->hashtagQuery = ''; $this->hashtagSuggestions = []; $this->showHashtagSuggestions = false; $this->currentHashtagStartPosition = null;
        $this->mentionQuery = ''; $this->mentionSuggestions = []; $this->showMentionSuggestions = false; $this->currentMentionStartPosition = null;
        // $this->activeAutocompleteContext = null; // Keep context until input focus changes or submission
        // $this->activeAutocompletePostId = null;
    }

    public function closeSuggestions()
    {
        $this->showHashtagSuggestions = false;
        $this->showMentionSuggestions = false;
        // Optionally reset queries here if needed when explicitly closing
        // $this->hashtagQuery = '';
        // $this->mentionQuery = '';
    }

    // --- End Autocomplete Methods ---

    public function getAvatar($userId)
    {
        $path = \App\Models\UserPhoto::where('user_id', $userId)
            ->latest()
            ->value('photo_path');
        return $path ? \Illuminate\Support\Facades\Storage::url($path) : asset('images/users/avatar.jpg');
    }

    public function render()
    {
        $isMember = Auth::check() ? Auth::user()->isMemberOf($this->group) : false;
        $canManage = Auth::check() ? Auth::user()->canManageGroup($this->group) : false;

        $posts = $this->group->posts()
                            ->with(['user.userPhotos', 'likedByUsers', 'comments.user.userPhotos'])
                            ->latest()
                            ->paginate(10);
        
        // Process content for display
        $posts->getCollection()->transform(function ($post) {
            $post->processed_content = ContentProcessor::processContent($post->content ?? '');
            $post->comments->transform(function ($comment) {
                $comment->processed_body = ContentProcessor::processContent($comment->body ?? '');
                return $comment;
            });
            return $post;
        });

        return view('livewire.groups.group-posts', [
            'posts' => $posts,
            'isMember' => $isMember,
            'canManage' => $canManage,
        ]);
    }
}
