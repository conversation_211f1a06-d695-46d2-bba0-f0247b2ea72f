{"private": true, "type": "module", "scripts": {"build": "vite build", "dev": "vite"}, "dependencies": {"@popperjs/core": "^2.11.8", "@tailwindcss/vite": "^4.0.17", "alpinejs": "^3.14.9", "autoprefixer": "^10.4.21", "axios": "^1.7.4", "bootstrap": "^5.3.3", "concurrently": "^9.0.1", "laravel-echo": "^2.1.4", "laravel-vite-plugin": "^1.0", "postcss": "^8.5.3", "pusher-js": "^8.4.0", "tailwindcss": "^4.0.17", "vite": "^6.0"}, "optionalDependencies": {"@rollup/rollup-linux-x64-gnu": "4.9.5", "@tailwindcss/oxide-linux-x64-gnu": "^4.0.1", "lightningcss-linux-x64-gnu": "^1.29.1"}}